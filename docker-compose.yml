version: '3.8'

services:
  # Backend Service
  backend:
    build:
      context: ./tabasco-backend
      dockerfile: Dockerfile
    ports:
      - "5004:5004"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=your-secret-key-here
      - DATABASE_URL=sqlite:///tabasco.db
      - CORS_ORIGINS=http://localhost:3000
    volumes:
      - backend_data:/app/instance
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5004/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Frontend Service
  frontend:
    build:
      context: ./tabasco-new-ui
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:5004
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

volumes:
  backend_data:
    driver: local

networks:
  default:
    name: tabasco-network
