#!/bin/bash

# GenAI Pricing Collector Setup Script

echo "GenAI Pricing Collector Setup"
echo "=============================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✓ Python 3 found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    echo "✓ Virtual environment created"
else
    echo "✓ Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
echo "Installing dependencies..."
source venv/bin/activate
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✓ Dependencies installed successfully"
else
    echo "✗ Failed to install dependencies"
    exit 1
fi

# Create output directory
mkdir -p output
echo "✓ Output directory created"

# Test the installation
echo ""
echo "Testing installation..."
python main.py --list-services

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Setup completed successfully!"
    echo ""
    echo "Usage examples:"
    echo "  source venv/bin/activate"
    echo "  python main.py --help"
    echo "  python main.py --dry-run"
    echo "  python main.py --service github_copilot"
    echo "  python main.py"
else
    echo "✗ Setup test failed"
    exit 1
fi
