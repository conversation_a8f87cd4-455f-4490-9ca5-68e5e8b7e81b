#!/bin/bash

# 🌶️ Tabasco Application Startup Script
# This script starts both backend and frontend services

set -e  # Exit on any error

echo "🌶️  Starting Tabasco Application"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[TABASCO]${NC} $1"
}

# Function to cleanup background processes on exit
cleanup() {
    print_status "Shutting down Tabasco services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend stopped"
    fi
    print_success "Tabasco application stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if we're in the right directory
if [ ! -d "tabasco-backend" ] || [ ! -d "tabasco-new-ui" ]; then
    print_error "tabasco-backend or tabasco-new-ui directories not found."
    print_error "Please run this script from the Tabasco project root directory."
    exit 1
fi

# Make scripts executable
print_status "Making startup scripts executable..."
chmod +x tabasco-backend/start-backend.sh
chmod +x tabasco-new-ui/start-frontend.sh
print_success "Scripts are now executable"

# Parse command line arguments
BACKEND_ONLY=false
FRONTEND_ONLY=false
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            HELP=true
            shift
            ;;
    esac
done

# Show help
if [ "$HELP" = true ]; then
    echo ""
    print_header "Tabasco Application Startup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --backend-only    Start only the backend service"
    echo "  --frontend-only   Start only the frontend service"
    echo "  --help, -h        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                Start both backend and frontend"
    echo "  $0 --backend-only Start only backend"
    echo "  $0 --frontend-only Start only frontend"
    echo ""
    exit 0
fi

# Start services based on options
if [ "$FRONTEND_ONLY" = true ]; then
    print_header "Starting Frontend Only..."
    cd tabasco-new-ui
    ./start-frontend.sh
elif [ "$BACKEND_ONLY" = true ]; then
    print_header "Starting Backend Only..."
    cd tabasco-backend
    ./start-backend.sh
else
    print_header "Starting Both Backend and Frontend..."

    # Start backend in background
    print_status "Starting backend service..."
    cd tabasco-backend
    ./start-backend.sh &
    BACKEND_PID=$!
    cd ..

    # Wait a moment for backend to start
    sleep 3

    # Start frontend in background
    print_status "Starting frontend service..."
    cd tabasco-new-ui
    ./start-frontend.sh &
    FRONTEND_PID=$!
    cd ..

    # Show status
    echo ""
    print_success "🚀 Tabasco Application Started!"
    print_success "📊 Backend: http://localhost:5004"
    print_success "🎨 Frontend: http://localhost:3000 (or next available port)"
    print_success "👤 Demo Login: demo / demo123"
    print_success "🔐 Admin Login: admin / password"
    echo ""
    print_status "Press Ctrl+C to stop all services"
    echo "=================================="

    # Wait for both processes
    wait $BACKEND_PID $FRONTEND_PID
fi
