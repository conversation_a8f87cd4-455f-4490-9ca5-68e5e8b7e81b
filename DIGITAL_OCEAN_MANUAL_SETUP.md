# 🚀 Digital Ocean Manual Deployment Guide

## 🎯 **Recommended Approach: Backend Only + Frontend on Vercel**

### **Step 1: Deploy Backend to Digital Ocean**

1. **Go to Digital Ocean App Platform**
   - Visit: https://cloud.digitalocean.com/apps
   - Click "Create App"

2. **Connect Repository**
   - Source: GitHub
   - Repository: `mbparsa/Tabasco`
   - Branch: `feature/real-usage-analytics`

3. **Configure Service**
   ```
   Service Name: tabasco-backend
   Source Directory: tabasco-backend
   Environment: Python
   Build Command: (auto-detected)
   Run Command: (leave empty - uses Procfile)
   HTTP Port: 8080
   ```

4. **Environment Variables**
   ```
   FLASK_ENV=production
   SECRET_KEY=your-secure-secret-key-here
   CORS_ORIGINS=*
   ```

5. **Deploy Backend**
   - Click "Create Resources"
   - Wait for deployment
   - Note the backend URL (e.g., `https://your-app.ondigitalocean.app`)

### **Step 2: Deploy Frontend to Vercel (Free & Easy)**

1. **Go to Vercel**
   - Visit: https://vercel.com
   - Sign up/login with GitHub

2. **Import Project**
   - Click "New Project"
   - Import `mbparsa/Tabasco`
   - Branch: `feature/real-usage-analytics`

3. **Configure Build**
   ```
   Framework Preset: Next.js
   Root Directory: tabasco-new-ui
   Build Command: npm run build
   Output Directory: .next
   ```

4. **Environment Variables**
   ```
   NEXT_PUBLIC_API_BASE_URL=https://your-backend-url.ondigitalocean.app
   NODE_ENV=production
   ```

5. **Deploy Frontend**
   - Click "Deploy"
   - Get frontend URL (e.g., `https://tabasco.vercel.app`)

### **Step 3: Update CORS**

1. **Update Backend CORS**
   - Go back to Digital Ocean app settings
   - Update environment variable:
   ```
   CORS_ORIGINS=https://tabasco.vercel.app,https://your-custom-domain.com
   ```

## 🔧 **Alternative: Backend-Only Deployment**

If you want to deploy only the backend and test it:

### **Manual Configuration**
1. **Service Settings**
   ```
   Name: tabasco-backend
   Source: GitHub (mbparsa/Tabasco, feature/real-usage-analytics)
   Source Directory: tabasco-backend
   Environment: Python
   ```

2. **Environment Variables**
   ```
   FLASK_ENV=production
   SECRET_KEY=tabasco-secret-key-2024
   CORS_ORIGINS=*
   PORT=8080
   ```

3. **Test Endpoints**
   - Health: `https://your-app.ondigitalocean.app/api/health`
   - Login: `https://your-app.ondigitalocean.app/api/login`
   - Dashboard: `https://your-app.ondigitalocean.app/api/dashboard/metrics`

## 🎯 **Why This Approach Works Better**

✅ **Simpler deployment** - Each service has one responsibility
✅ **Better performance** - Frontend served from CDN (Vercel)
✅ **Easier debugging** - Separate logs and monitoring
✅ **Cost effective** - Vercel free tier for frontend
✅ **Faster builds** - No complex multi-service coordination

## 🔍 **Testing Your Deployment**

### **Backend Tests**
```bash
# Health check
curl https://your-backend.ondigitalocean.app/api/health

# Login test
curl -X POST https://your-backend.ondigitalocean.app/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

### **Frontend Tests**
- Visit your Vercel URL
- Try logging in with: `admin` / `password`
- Check that dashboard loads with real data

## 🚨 **Troubleshooting**

### **Backend Issues**
- Check Digital Ocean build logs
- Verify Procfile exists: `web: python app.py`
- Check environment variables are set

### **Frontend Issues**
- Verify `NEXT_PUBLIC_API_BASE_URL` points to backend
- Check browser console for CORS errors
- Ensure backend CORS includes frontend domain

### **CORS Issues**
- Backend CORS must include frontend domain
- Use `*` for testing, specific domains for production
- Check browser network tab for preflight requests
