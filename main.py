"""Main application entry point for GenAI pricing collector."""

import argparse
import sys
import json
from datetime import datetime

from scraper import PricingScraper
from publisher import PricingPublisher
from config import SERVICES_CONFIG


def main():
    """Main application function."""
    parser = argparse.ArgumentParser(
        description="GenAI Coding Assistant Pricing Collector",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Scrape all services and publish to YAML/JSON
  python main.py --service github_copilot # Scrape only GitHub Copilot
  python main.py --format json           # Output only JSON format
  python main.py --list-services         # List available services
        """
    )
    
    parser.add_argument(
        '--service', '-s',
        choices=list(SERVICES_CONFIG.keys()),
        help='Scrape pricing for a specific service only'
    )
    
    parser.add_argument(
        '--format', '-f',
        choices=['yaml', 'json', 'both', 'summary'],
        default='both',
        help='Output format (default: both)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        default='output',
        help='Output directory for generated files (default: output)'
    )
    
    parser.add_argument(
        '--list-services', '-l',
        action='store_true',
        help='List all available services and exit'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be scraped without actually scraping'
    )
    
    args = parser.parse_args()
    
    # List services and exit
    if args.list_services:
        print("Available GenAI coding assistant services:")
        print("-" * 50)
        for key, config in SERVICES_CONFIG.items():
            print(f"  {key:15} - {config['name']}")
            print(f"  {'':15}   URL: {config['url']}")
            print()
        return 0
    
    # Dry run mode
    if args.dry_run:
        print("DRY RUN MODE - No actual scraping will be performed")
        print("-" * 60)
        
        if args.service:
            services_to_scrape = [args.service]
        else:
            services_to_scrape = list(SERVICES_CONFIG.keys())
        
        print(f"Would scrape {len(services_to_scrape)} service(s):")
        for service_key in services_to_scrape:
            config = SERVICES_CONFIG[service_key]
            print(f"  - {config['name']} ({config['url']})")
        
        print(f"\nOutput format: {args.format}")
        print(f"Output directory: {args.output_dir}")
        return 0
    
    # Initialize components
    scraper = PricingScraper()
    publisher = PricingPublisher()
    
    # Override output directory if specified
    if args.output_dir != 'output':
        publisher.output_dir = args.output_dir
        publisher._ensure_output_directory()
    
    try:
        print("GenAI Pricing Collector")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Scrape data
        if args.service:
            print(f"Scraping single service: {SERVICES_CONFIG[args.service]['name']}")
            service_data = scraper.scrape_service(args.service)
            
            # Format single service data to match expected structure
            data = {
                'services': {args.service: service_data},
                'metadata': {
                    'collection_timestamp': datetime.now().isoformat(),
                    'total_services': 1,
                    'successful_scrapes': 1 if service_data.get('status') == 'success' else 0
                }
            }
        else:
            print("Scraping all configured services...")
            data = scraper.scrape_all_services()
        
        # Print scraping results
        print("\nScraping Results:")
        print("-" * 30)
        
        services = data.get('services', {})
        for service_key, service_data in services.items():
            status = service_data.get('status', 'unknown')
            name = service_data.get('name', service_key)
            
            if status == 'success':
                pricing_count = len(service_data.get('pricing', {}))
                print(f"  ✓ {name}: {pricing_count} pricing tier(s) found")
                
                if args.verbose:
                    for plan_name, plan_data in service_data.get('pricing', {}).items():
                        price = plan_data.get('price', 'N/A')
                        currency = plan_data.get('currency', 'USD')
                        period = plan_data.get('period', 'month')
                        print(f"    - {plan_name}: {currency} {price}/{period}")
            else:
                print(f"  ✗ {name}: Failed ({status})")
                if args.verbose and service_data.get('error'):
                    print(f"    Error: {service_data['error']}")
        
        # Publish data
        print(f"\nPublishing data in {args.format} format(s)...")
        print("-" * 40)
        
        published_files = {}
        
        if args.format in ['yaml', 'both']:
            yaml_file = publisher.publish_yaml(data)
            published_files['yaml'] = yaml_file
        
        if args.format in ['json', 'both']:
            json_file = publisher.publish_json(data)
            published_files['json'] = json_file
        
        if args.format == 'summary':
            summary_file = publisher.publish_summary_yaml(data)
            published_files['summary'] = summary_file
        
        # Create API-friendly format
        api_data = publisher.create_api_endpoint_data(data)
        api_file = publisher.publish_json(api_data, "api_pricing.json")
        published_files['api'] = api_file
        
        # Summary
        metadata = data.get('metadata', {})
        total_services = metadata.get('total_services', 0)
        successful_scrapes = metadata.get('successful_scrapes', 0)
        
        print(f"\nCollection Summary:")
        print("-" * 30)
        print(f"Total services configured: {total_services}")
        print(f"Successful scrapes: {successful_scrapes}")
        print(f"Success rate: {(successful_scrapes/total_services*100):.1f}%" if total_services > 0 else "N/A")
        
        print(f"\nGenerated files:")
        for format_type, filepath in published_files.items():
            if filepath:
                print(f"  {format_type.upper()}: {filepath}")
        
        print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        return 1
    except Exception as e:
        print(f"\nError: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
    finally:
        # Cleanup
        scraper.cleanup()


if __name__ == "__main__":
    sys.exit(main())
