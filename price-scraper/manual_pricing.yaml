# Manual pricing data for services that are difficult to scrape automatically
# This file should be updated manually when pricing changes are detected
# Last updated: 2025-05-24

manual_pricing_data:
  cursor:
    name: "Cursor"
    url: "https://cursor.sh/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "Pro plan with advanced AI features"
      business:
        price: 40.0
        currency: "USD"
        billing_period: "month"
        notes: "Business plan with team features"

  codeium:
    name: "Codeium"
    url: "https://codeium.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      individual:
        price: 12.0
        currency: "USD"
        billing_period: "month"
        notes: "Individual developer plan"
      teams:
        price: 35.0
        currency: "USD"
        billing_period: "month"
        notes: "Team collaboration features"

  tabnine:
    name: "Tabnine"
    url: "https://www.tabnine.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 12.0
        currency: "USD"
        billing_period: "month"
        notes: "Pro plan with advanced completions"
      enterprise:
        price: 39.0
        currency: "USD"
        billing_period: "month"
        notes: "Enterprise plan with custom models"

  github_copilot:
    name: "GitHub Copilot"
    url: "https://github.com/features/copilot"
    last_manual_update: "2025-05-24"
    pricing_plans:
      individual:
        price: 10.0
        currency: "USD"
        billing_period: "month"
        notes: "For individual developers"
      business:
        price: 19.0
        currency: "USD"
        billing_period: "month"
        notes: "For organizations and businesses"

  openai_chatgpt:
    name: "OpenAI ChatGPT Plus"
    url: "https://openai.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      plus:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "ChatGPT Plus subscription"
      pro:
        price: 200.0
        currency: "USD"
        billing_period: "month"
        notes: "ChatGPT Pro subscription"

  anthropic_claude:
    name: "Anthropic Claude"
    url: "https://www.anthropic.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "Claude Pro subscription"
      team:
        price: 25.0
        currency: "USD"
        billing_period: "month"
        notes: "Claude for Teams"

  google_gemini:
    name: "Google Gemini Advanced"
    url: "https://gemini.google.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      advanced:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "Gemini Advanced subscription"

  notion_ai:
    name: "Notion AI"
    url: "https://www.notion.so/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      ai_addon:
        price: 10.0
        currency: "USD"
        billing_period: "month"
        notes: "AI add-on for Notion"

  jasper:
    name: "Jasper"
    url: "https://www.jasper.ai/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      creator:
        price: 39.0
        currency: "USD"
        billing_period: "month"
        notes: "Jasper Creator plan"
      pro:
        price: 99.0
        currency: "USD"
        billing_period: "month"
        notes: "Jasper Pro plan"

  grammarly:
    name: "Grammarly Business"
    url: "https://www.grammarly.com/business/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      business:
        price: 15.0
        currency: "USD"
        billing_period: "month"
        notes: "Grammarly Business subscription"

  midjourney:
    name: "Midjourney"
    url: "https://docs.midjourney.com/docs/plans"
    last_manual_update: "2025-05-24"
    pricing_plans:
      basic:
        price: 10.0
        currency: "USD"
        billing_period: "month"
        notes: "Basic plan"
      standard:
        price: 30.0
        currency: "USD"
        billing_period: "month"
        notes: "Standard plan"
      pro:
        price: 60.0
        currency: "USD"
        billing_period: "month"
        notes: "Pro plan"

  perplexity:
    name: "Perplexity Pro"
    url: "https://www.perplexity.ai/pro"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "Perplexity Pro subscription"

# Instructions for updating this file:
# 1. Visit the service's pricing page
# 2. Update the pricing_plans section with current prices
# 3. Update the last_manual_update date
# 4. Add notes about plan features if helpful
# 5. Run the scraper to regenerate output files
