# 🔍 Price Scraper

Automated price collection tool for various e-commerce platforms.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip

### Installation

1. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies:**
```bash
pip install -r scraper-requirements.txt
```

3. **Run the scraper:**
```bash
python main.py
```

## 🔧 Configuration

Configure your scraping targets and settings in the YAML configuration files.

## 📊 Features

- **Multi-platform support** - Scrape from various e-commerce sites
- **Automated data collection** - Schedule regular price updates
- **Data export** - Export collected data in various formats
- **Error handling** - Robust error handling and retry mechanisms

## 🛠️ Development

This is the original price scraping functionality that was part of the PriceCollector project.
It has been separated from the Tabasco ROI calculator for better organization.
