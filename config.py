"""Configuration settings for the GenAI pricing collector."""

import os
from typing import Dict, List

# Target services to scrape pricing information from
SERVICES_CONFIG = {
    "github_copilot": {
        "name": "GitHub Copilot",
        "url": "https://github.com/features/copilot",
        "category": "Code Generation",
        "provider": "Microsoft",
        "selectors": {
            "individual_price": ".pricing-card",
            "business_price": ".pricing-card"
        }
    },
    "openai_chatgpt": {
        "name": "OpenAI ChatGPT Plus",
        "url": "https://openai.com/pricing",
        "category": "General AI Assistant",
        "provider": "OpenAI",
        "selectors": {
            "plus_price": ".pricing-tier",
            "pro_price": ".pricing-tier"
        }
    },
    "anthropic_claude": {
        "name": "Anthropic Claude",
        "url": "https://www.anthropic.com/pricing",
        "category": "General AI Assistant",
        "provider": "Anthropic",
        "selectors": {
            "pro_price": ".pricing-card",
            "team_price": ".pricing-card"
        }
    },
    "google_gemini": {
        "name": "Google Gemini Advanced",
        "url": "https://gemini.google.com/pricing",
        "category": "General AI Assistant",
        "provider": "Google",
        "selectors": {
            "advanced_price": ".pricing-tier",
            "business_price": ".pricing-tier"
        }
    },
    "cursor": {
        "name": "Cursor",
        "url": "https://cursor.sh/pricing",
        "category": "Code Generation",
        "provider": "Cursor",
        "selectors": {
            "pro_price": ".pricing-tier",
            "business_price": ".pricing-tier"
        }
    },
    "codeium": {
        "name": "Codeium",
        "url": "https://codeium.com/pricing",
        "category": "Code Generation",
        "provider": "Codeium",
        "selectors": {
            "individual_price": ".pricing-card",
            "teams_price": ".pricing-card"
        }
    },
    "tabnine": {
        "name": "Tabnine",
        "url": "https://www.tabnine.com/pricing",
        "category": "Code Generation",
        "provider": "Tabnine",
        "selectors": {
            "pro_price": ".pricing-tier",
            "enterprise_price": ".pricing-tier"
        }
    },
    "notion_ai": {
        "name": "Notion AI",
        "url": "https://www.notion.so/pricing",
        "category": "Productivity",
        "provider": "Notion",
        "selectors": {
            "ai_price": ".pricing-card",
            "team_price": ".pricing-card"
        }
    },
    "jasper": {
        "name": "Jasper",
        "url": "https://www.jasper.ai/pricing",
        "category": "Content Creation",
        "provider": "Jasper",
        "selectors": {
            "creator_price": ".pricing-tier",
            "pro_price": ".pricing-tier"
        }
    },
    "grammarly": {
        "name": "Grammarly Business",
        "url": "https://www.grammarly.com/business/pricing",
        "category": "Writing Assistant",
        "provider": "Grammarly",
        "selectors": {
            "business_price": ".pricing-card",
            "enterprise_price": ".pricing-card"
        }
    },
    "midjourney": {
        "name": "Midjourney",
        "url": "https://docs.midjourney.com/docs/plans",
        "category": "Image Generation",
        "provider": "Midjourney",
        "selectors": {
            "basic_price": ".pricing-tier",
            "standard_price": ".pricing-tier"
        }
    },
    "perplexity": {
        "name": "Perplexity Pro",
        "url": "https://www.perplexity.ai/pro",
        "category": "General AI Assistant",
        "provider": "Perplexity",
        "selectors": {
            "pro_price": ".pricing-card"
        }
    }
}

# Output configuration
OUTPUT_CONFIG = {
    "yaml_file": "genai_pricing.yaml",
    "json_file": "genai_pricing.json",
    "update_interval_hours": 5,  # Updated to 5 hours
    "backup_interval_hours": 24,  # Daily backup
    "max_backup_files": 7  # Keep 7 days of backups
}

# Scraping configuration
SCRAPING_CONFIG = {
    "timeout": 30,
    "retry_attempts": 3,
    "delay_between_requests": 3,  # Increased delay to be more respectful
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "enable_selenium_fallback": True,
    "max_concurrent_requests": 2,  # Limit concurrent requests
    "respect_robots_txt": True
}
