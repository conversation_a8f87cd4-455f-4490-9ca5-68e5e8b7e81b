# 🔧 Tabasco Configuration Guide

This document explains how to configure the Tabasco application for different environments and deployments.

## 📁 Configuration Files

### Frontend Configuration (`tabasco-new-ui/`)

#### `.env.local` (Development)
```bash
# Backend API Base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:5004

# Frontend Port
PORT=3000

# Development/Production Mode
NODE_ENV=development
```

#### `.env.example` (Template)
Copy this file to `.env.local` and modify as needed for your environment.

### Backend Configuration (`tabasco-backend/`)

#### `.env` (Development)
```bash
# Server Configuration
HOST=127.0.0.1
PORT=5004
DEBUG=True

# Security
SECRET_KEY=a_very_secret_key_that_should_be_changed_in_production

# Database Configuration
DATABASE_URL=sqlite:///instance/app.db

# CORS Configuration (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
```

#### `.env.example` (Template)
Copy this file to `.env` and modify as needed for your environment.

## 🌍 Environment Variables

### Frontend Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_API_BASE_URL` | Backend API URL | `http://localhost:5004` | No |
| `PORT` | Frontend port | `3000` | No |
| `NODE_ENV` | Environment mode | `development` | No |

### Backend Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `HOST` | Server host | `127.0.0.1` | No |
| `PORT` | Server port | `5004` | No |
| `DEBUG` | Debug mode | `True` | No |
| `SECRET_KEY` | Flask secret key | Auto-generated | **Yes** |
| `DATABASE_URL` | Database connection | SQLite local | No |
| `CORS_ORIGINS` | Allowed origins | localhost:3000-3007 | No |

## 🚀 Deployment Configurations

### Development
```bash
# Frontend
NEXT_PUBLIC_API_BASE_URL=http://localhost:5004
NODE_ENV=development

# Backend
HOST=127.0.0.1
PORT=5004
DEBUG=True
```

### Production
```bash
# Frontend
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NODE_ENV=production

# Backend
HOST=0.0.0.0
PORT=5004
DEBUG=False
SECRET_KEY=your_secure_secret_key_here
DATABASE_URL=postgresql://user:pass@host:port/dbname
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## 🔒 Security Notes

1. **Always change `SECRET_KEY` in production**
2. **Use HTTPS in production**
3. **Restrict CORS origins to your domains only**
4. **Use environment-specific database credentials**
5. **Never commit `.env` files to version control**

## 🚀 Quick Start (Automated)

### Option 1: Start Everything (Recommended)
```bash
./start-tabasco.sh
```

### Option 2: Start Services Individually
```bash
# Backend only
./start-tabasco.sh --backend-only

# Frontend only
./start-tabasco.sh --frontend-only
```

### Option 3: Manual Service Start
```bash
# Backend
cd tabasco-backend && ./start-backend.sh

# Frontend
cd tabasco-new-ui && ./start-frontend.sh
```

### Windows Users
```batch
REM Backend
cd tabasco-backend
start-backend.bat

REM Frontend
cd tabasco-new-ui
start-frontend.bat
```

## 📝 Manual Setup Instructions

1. **Copy environment files:**
   ```bash
   cp tabasco-new-ui/.env.example tabasco-new-ui/.env.local
   cp tabasco-backend/.env.example tabasco-backend/.env
   ```

2. **Modify variables as needed**

3. **Install dependencies:**
   ```bash
   cd tabasco-backend && pip install -r requirements.txt
   cd ../tabasco-new-ui && npm install
   ```

4. **Start services:**
   ```bash
   # Backend
   cd tabasco-backend && python app.py

   # Frontend
   cd tabasco-new-ui && npm run dev
   ```

## 🔄 No More Hardcoded Paths!

All paths and URLs are now configurable through environment variables. This ensures:
- ✅ **Portability** across different environments
- ✅ **Security** through environment-specific secrets
- ✅ **Flexibility** for different deployment scenarios
- ✅ **Maintainability** with centralized configuration
