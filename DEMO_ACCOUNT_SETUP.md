# 🎭 Demo Account Setup Guide

## 📋 Environment Variables for Demo Account

Add these environment variables to your Digital Ocean App Platform:

### **Demo User Credentials**
```
DEMO_USERNAME=demo
DEMO_PASSWORD=demo123
DEMO_EMAIL=<EMAIL>
```

### **Current Admin Credentials** (already set)
```
ADMIN_USERNAME=your_secure_username
ADMIN_PASSWORD=your_secure_password_123!
ADMIN_EMAIL=<EMAIL>
```

## 🔧 How to Add Demo Account

### **Step 1: Update Digital Ocean Environment Variables**

1. **Go to Digital Ocean App Platform**
   - Navigate to your Tabasco app
   - Go to Settings → Environment Variables

2. **Add Demo User Variables:**
   ```
   DEMO_USERNAME = demo
   DEMO_PASSWORD = demo123
   DEMO_EMAIL = <EMAIL>
   ```

3. **Save and Redeploy**
   - Click "Save"
   - App will automatically redeploy

### **Step 2: Test Demo Account**

After deployment completes:

1. **Visit:** https://tabasco-teal.vercel.app
2. **Login with demo credentials:**
   - **Username:** `demo`
   - **Password:** `demo123`
3. **Verify access** to dashboard and features

## 🎯 Demo Account Features

### **What Demo Users Can Access:**
✅ **Login/Logout** - Full authentication  
✅ **Dashboard** - View analytics and metrics  
✅ **Employees Page** - View/manage demo employee data  
✅ **AI Tools Page** - View/manage demo AI tools  
✅ **ROI Calculator** - Calculate ROI with demo data  

### **Demo Data Included:**
- **2 Demo Employees** with different job families and levels
- **2 Demo AI Tools** with usage statistics
- **Separate data** from admin account
- **Realistic metrics** for demonstration

## 🔒 Security Features

✅ **Environment Variable Based** - No hardcoded credentials  
✅ **Separate User Accounts** - Demo and admin are isolated  
✅ **JWT Token Authentication** - Secure cross-origin auth  
✅ **Production Ready** - Same security as admin account  

## 🎭 Use Cases for Demo Account

### **Client Demonstrations:**
- Show app functionality without sharing admin credentials
- Safe environment for client testing
- Realistic data for presentations

### **User Testing:**
- Allow stakeholders to test features
- Gather feedback without admin access
- Training and onboarding

### **Development Testing:**
- Test user permissions and data isolation
- Verify authentication flows
- Debug user-specific issues

## 🚀 Quick Setup Commands

If you prefer to set environment variables via CLI:

```bash
# Using doctl (Digital Ocean CLI)
doctl apps update YOUR_APP_ID --spec .do/app.yaml

# Or update individual variables
doctl apps update-env YOUR_APP_ID DEMO_USERNAME=demo
doctl apps update-env YOUR_APP_ID DEMO_PASSWORD=demo123
doctl apps update-env YOUR_APP_ID DEMO_EMAIL=<EMAIL>
```

## 📊 Expected Results

After setup, you'll have:

1. **Admin Account:** Full access with complete dataset
2. **Demo Account:** Limited access with demo dataset
3. **Isolated Data:** Each user sees only their own data
4. **Secure Authentication:** Both accounts use JWT tokens

## 🔍 Verification Steps

1. **Login as admin** - Should see full employee/tool dataset
2. **Logout and login as demo** - Should see demo dataset only
3. **Check data isolation** - Demo user can't see admin data
4. **Test all features** - Both accounts should have full functionality

## 🎯 Demo Credentials Summary

**For sharing with clients/stakeholders:**

```
Demo URL: https://tabasco-teal.vercel.app
Username: demo
Password: demo123
```

**Features available:**
- Real-time ROI calculations
- Employee management
- AI tools tracking
- Usage analytics dashboard
- Interactive charts and metrics
