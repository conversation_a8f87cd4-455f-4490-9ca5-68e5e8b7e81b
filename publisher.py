"""Publisher module for outputting pricing data in various formats."""

import json
import yaml
import os
from datetime import datetime
from typing import Dict, Any

from config import OUTPUT_CONFIG


class PricingPublisher:
    """Handles publishing pricing data to different formats."""
    
    def __init__(self):
        self.output_dir = "output"
        self._ensure_output_directory()
    
    def _ensure_output_directory(self):
        """Create output directory if it doesn't exist."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def publish_yaml(self, data: Dict[str, Any], filename: str = None) -> str:
        """Publish pricing data as YAML file."""
        if filename is None:
            filename = OUTPUT_CONFIG['yaml_file']
        
        filepath = os.path.join(self.output_dir, filename)
        
        # Format data for better YAML readability
        formatted_data = self._format_for_yaml(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            yaml.dump(formatted_data, f, default_flow_style=False, 
                     allow_unicode=True, sort_keys=False, indent=2)
        
        print(f"YAML data published to: {filepath}")
        return filepath
    
    def publish_json(self, data: Dict[str, Any], filename: str = None) -> str:
        """Publish pricing data as JSON file."""
        if filename is None:
            filename = OUTPUT_CONFIG['json_file']
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"JSON data published to: {filepath}")
        return filepath
    
    def publish_summary_yaml(self, data: Dict[str, Any]) -> str:
        """Publish a simplified summary in YAML format."""
        summary = self._create_summary(data)
        return self.publish_yaml(summary, "pricing_summary.yaml")
    
    def _format_for_yaml(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format data structure for better YAML output."""
        formatted = {
            'genai_pricing_data': {
                'metadata': data.get('metadata', {}),
                'services': {}
            }
        }
        
        services = data.get('services', {})
        for service_key, service_data in services.items():
            formatted_service = {
                'name': service_data.get('name', ''),
                'url': service_data.get('url', ''),
                'status': service_data.get('status', ''),
                'last_updated': service_data.get('last_updated', ''),
                'pricing_plans': {}
            }
            
            pricing = service_data.get('pricing', {})
            for plan_name, plan_data in pricing.items():
                formatted_service['pricing_plans'][plan_name] = {
                    'price': plan_data.get('price', 0),
                    'currency': plan_data.get('currency', 'USD'),
                    'billing_period': plan_data.get('period', 'month')
                }
            
            if service_data.get('error'):
                formatted_service['error'] = service_data['error']
            
            formatted['genai_pricing_data']['services'][service_key] = formatted_service
        
        return formatted
    
    def _create_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a simplified summary of pricing data."""
        summary = {
            'pricing_summary': {
                'generated_at': datetime.now().isoformat(),
                'services_count': data.get('metadata', {}).get('total_services', 0),
                'successful_scrapes': data.get('metadata', {}).get('successful_scrapes', 0),
                'services': {}
            }
        }
        
        services = data.get('services', {})
        for service_key, service_data in services.items():
            if service_data.get('status') == 'success':
                pricing = service_data.get('pricing', {})
                service_summary = {
                    'name': service_data.get('name', ''),
                    'plans': {}
                }
                
                for plan_name, plan_data in pricing.items():
                    price = plan_data.get('price', 0)
                    currency = plan_data.get('currency', 'USD')
                    period = plan_data.get('period', 'month')
                    service_summary['plans'][plan_name] = f"{currency} {price}/{period}"
                
                summary['pricing_summary']['services'][service_key] = service_summary
        
        return summary
    
    def publish_all_formats(self, data: Dict[str, Any]) -> Dict[str, str]:
        """Publish data in all supported formats."""
        results = {}
        
        try:
            results['yaml'] = self.publish_yaml(data)
        except Exception as e:
            print(f"Failed to publish YAML: {e}")
            results['yaml'] = None
        
        try:
            results['json'] = self.publish_json(data)
        except Exception as e:
            print(f"Failed to publish JSON: {e}")
            results['json'] = None
        
        try:
            results['summary'] = self.publish_summary_yaml(data)
        except Exception as e:
            print(f"Failed to publish summary: {e}")
            results['summary'] = None
        
        return results
    
    def get_latest_data(self, format_type: str = 'yaml') -> Dict[str, Any]:
        """Read the latest published data."""
        if format_type == 'yaml':
            filename = OUTPUT_CONFIG['yaml_file']
            filepath = os.path.join(self.output_dir, filename)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
        
        elif format_type == 'json':
            filename = OUTPUT_CONFIG['json_file']
            filepath = os.path.join(self.output_dir, filename)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        
        return {}
    
    def create_api_endpoint_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create data structure suitable for API consumption."""
        api_data = {
            'api_version': '1.0',
            'timestamp': datetime.now().isoformat(),
            'data_freshness': data.get('metadata', {}).get('collection_timestamp', ''),
            'services': []
        }
        
        services = data.get('services', {})
        for service_key, service_data in services.items():
            if service_data.get('status') == 'success':
                service_api = {
                    'id': service_key,
                    'name': service_data.get('name', ''),
                    'provider_url': service_data.get('url', ''),
                    'last_updated': service_data.get('last_updated', ''),
                    'pricing_tiers': []
                }
                
                pricing = service_data.get('pricing', {})
                for plan_name, plan_data in pricing.items():
                    tier = {
                        'tier_name': plan_name,
                        'price': plan_data.get('price', 0),
                        'currency': plan_data.get('currency', 'USD'),
                        'billing_cycle': plan_data.get('period', 'month')
                    }
                    service_api['pricing_tiers'].append(tier)
                
                api_data['services'].append(service_api)
        
        return api_data
