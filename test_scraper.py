#!/usr/bin/env python3
"""
Test script to validate scraper functionality and data quality.
Run this to ensure the scraper is working correctly.
"""

import sys
import json
import yaml
from datetime import datetime
from pathlib import Path

from scraper import PricingScraper
from publisher import PricingPublisher
from config import SERVICES_CONFIG, OUTPUT_CONFIG

def test_single_service(service_key: str):
    """Test scraping a single service."""
    print(f"\n🔍 Testing {service_key}...")
    
    scraper = PricingScraper()
    try:
        result = scraper.scrape_service(service_key)
        
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   Data source: {result.get('data_source', 'unknown')}")
        
        pricing = result.get('pricing', {})
        if pricing:
            print(f"   Plans found: {len(pricing)}")
            for plan_name, plan_data in pricing.items():
                price = plan_data.get('price', 0)
                currency = plan_data.get('currency', 'USD')
                period = plan_data.get('period', 'month')
                print(f"     - {plan_name}: {currency} {price}/{period}")
        else:
            print("   ⚠️  No pricing data found")
        
        return result.get('status') == 'success'
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    finally:
        scraper.cleanup()

def test_all_services():
    """Test scraping all configured services."""
    print("🚀 Testing all services...")
    print("=" * 50)
    
    scraper = PricingScraper()
    results = {}
    
    try:
        for service_key in SERVICES_CONFIG.keys():
            success = test_single_service(service_key)
            results[service_key] = success
    finally:
        scraper.cleanup()
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("-" * 30)
    
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    for service_key, success in results.items():
        service_name = SERVICES_CONFIG[service_key]['name']
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {service_name}")
    
    print(f"\nOverall: {successful}/{total} services successful ({successful/total*100:.1f}%)")
    
    return successful >= total * 0.5  # Consider successful if 50%+ pass

def test_data_validation():
    """Test data validation and format conversion."""
    print("\n🔍 Testing data validation...")
    
    # Test with sample data
    sample_data = {
        'services': {
            'test_service': {
                'name': 'Test Service',
                'status': 'success',
                'pricing': {
                    'pro': {'price': 20.0, 'currency': 'USD', 'period': 'month'}
                },
                'last_updated': datetime.now().isoformat()
            }
        },
        'metadata': {
            'collection_timestamp': datetime.now().isoformat(),
            'total_services': 1,
            'successful_scrapes': 1
        }
    }
    
    try:
        publisher = PricingPublisher()
        
        # Test YAML publishing
        yaml_file = publisher.publish_yaml(sample_data, "test_output.yaml")
        print(f"   ✅ YAML publishing: {yaml_file}")
        
        # Test JSON publishing
        json_file = publisher.publish_json(sample_data, "test_output.json")
        print(f"   ✅ JSON publishing: {json_file}")
        
        # Test API format
        api_data = publisher.create_api_endpoint_data(sample_data)
        print(f"   ✅ API format conversion: {len(api_data.get('services', []))} services")
        
        # Cleanup test files
        Path(yaml_file).unlink(missing_ok=True)
        Path(json_file).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data validation failed: {e}")
        return False

def test_manual_fallback():
    """Test manual pricing fallback functionality."""
    print("\n🔍 Testing manual pricing fallback...")
    
    scraper = PricingScraper()
    
    try:
        # Test loading manual pricing for a service
        manual_pricing = scraper._load_manual_pricing('github_copilot')
        
        if manual_pricing:
            print(f"   ✅ Manual pricing loaded: {len(manual_pricing)} plans")
            for plan_name, plan_data in manual_pricing.items():
                price = plan_data.get('price', 0)
                currency = plan_data.get('currency', 'USD')
                period = plan_data.get('period', 'month')
                print(f"     - {plan_name}: {currency} {price}/{period}")
            return True
        else:
            print("   ⚠️  No manual pricing data found")
            return False
            
    except Exception as e:
        print(f"   ❌ Manual fallback test failed: {e}")
        return False
    finally:
        scraper.cleanup()

def test_output_files():
    """Test that output files are created and valid."""
    print("\n🔍 Testing output file generation...")
    
    try:
        # Check if output files exist
        output_dir = Path("output")
        yaml_file = output_dir / OUTPUT_CONFIG['yaml_file']
        json_file = output_dir / OUTPUT_CONFIG['json_file']
        
        files_exist = yaml_file.exists() and json_file.exists()
        print(f"   Output files exist: {'✅' if files_exist else '❌'}")
        
        if yaml_file.exists():
            # Test YAML validity
            with open(yaml_file, 'r') as f:
                yaml_data = yaml.safe_load(f)
            print(f"   ✅ YAML file is valid")
            
            services = yaml_data.get('genai_pricing_data', {}).get('services', {})
            print(f"   Services in YAML: {len(services)}")
        
        if json_file.exists():
            # Test JSON validity
            with open(json_file, 'r') as f:
                json_data = json.load(f)
            print(f"   ✅ JSON file is valid")
            
            services = json_data.get('services', {})
            print(f"   Services in JSON: {len(services)}")
        
        return files_exist
        
    except Exception as e:
        print(f"   ❌ Output file test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🌶️ Tabasco Scraper Test Suite")
    print("=" * 60)
    
    tests = [
        ("Service Scraping", test_all_services),
        ("Data Validation", test_data_validation),
        ("Manual Fallback", test_manual_fallback),
        ("Output Files", test_output_files)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"   💥 Test crashed: {e}")
            results[test_name] = False
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 Test Suite Results:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Scraper is working correctly.")
        return 0
    elif passed >= total * 0.7:
        print("⚠️  Most tests passed. Some issues may need attention.")
        return 1
    else:
        print("❌ Multiple test failures. Scraper needs fixing.")
        return 2

if __name__ == "__main__":
    sys.exit(main())
