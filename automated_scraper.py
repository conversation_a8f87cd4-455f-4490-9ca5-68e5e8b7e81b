#!/usr/bin/env python3
"""
Automated scraper that runs every 5 hours to collect AI tool pricing data.
Includes error handling, logging, and data validation.
"""

import schedule
import time
import logging
import os
import shutil
import json
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any

from scraper import PricingScraper
from publisher import PricingPublisher
from config import OUTPUT_CONFIG, SERVICES_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedScraper:
    """Automated scraper with scheduling and monitoring."""
    
    def __init__(self):
        self.scraper = PricingScraper()
        self.publisher = PricingPublisher()
        self.output_dir = "output"
        self.backup_dir = "backups"
        self.stats_file = "scraper_stats.json"
        
        # Ensure directories exist
        Path(self.output_dir).mkdir(exist_ok=True)
        Path(self.backup_dir).mkdir(exist_ok=True)
        
        # Load or initialize stats
        self.stats = self._load_stats()
    
    def _load_stats(self) -> Dict[str, Any]:
        """Load scraper statistics."""
        if os.path.exists(self.stats_file):
            try:
                with open(self.stats_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load stats: {e}")
        
        return {
            'total_runs': 0,
            'successful_runs': 0,
            'last_run': None,
            'last_successful_run': None,
            'services_success_rate': {},
            'errors': []
        }
    
    def _save_stats(self):
        """Save scraper statistics."""
        try:
            with open(self.stats_file, 'w') as f:
                json.dump(self.stats, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save stats: {e}")
    
    def _create_backup(self):
        """Create backup of current pricing data."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Backup YAML file
            yaml_file = os.path.join(self.output_dir, OUTPUT_CONFIG['yaml_file'])
            if os.path.exists(yaml_file):
                backup_yaml = os.path.join(self.backup_dir, f"pricing_{timestamp}.yaml")
                shutil.copy2(yaml_file, backup_yaml)
                logger.info(f"Created backup: {backup_yaml}")
            
            # Cleanup old backups
            self._cleanup_old_backups()
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
    
    def _cleanup_old_backups(self):
        """Remove old backup files."""
        try:
            backup_files = list(Path(self.backup_dir).glob("pricing_*.yaml"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            max_backups = OUTPUT_CONFIG.get('max_backup_files', 7)
            for old_backup in backup_files[max_backups:]:
                old_backup.unlink()
                logger.info(f"Removed old backup: {old_backup}")
                
        except Exception as e:
            logger.error(f"Failed to cleanup backups: {e}")
    
    def _validate_pricing_data(self, data: Dict[str, Any]) -> bool:
        """Validate scraped pricing data."""
        try:
            services = data.get('services', {})
            if not services:
                logger.warning("No services data found")
                return False
            
            successful_services = 0
            total_services = len(services)
            
            for service_key, service_data in services.items():
                if service_data.get('status') == 'success':
                    pricing = service_data.get('pricing', {})
                    if pricing:
                        successful_services += 1
                        logger.info(f"✓ {service_data.get('name', service_key)}: {len(pricing)} plans")
                    else:
                        logger.warning(f"⚠ {service_data.get('name', service_key)}: No pricing data")
                else:
                    logger.warning(f"✗ {service_data.get('name', service_key)}: Failed")
            
            success_rate = (successful_services / total_services) * 100
            logger.info(f"Success rate: {success_rate:.1f}% ({successful_services}/{total_services})")
            
            # Consider successful if at least 50% of services scraped successfully
            return success_rate >= 50.0
            
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            return False
    
    def _update_tabasco_backend(self):
        """Copy pricing data to Tabasco backend directory."""
        try:
            yaml_file = os.path.join(self.output_dir, OUTPUT_CONFIG['yaml_file'])
            backend_file = os.path.join('tabasco-backend', 'genai_tool_pricing.yaml')
            
            if os.path.exists(yaml_file):
                # Convert scraper format to Tabasco format
                self._convert_to_tabasco_format(yaml_file, backend_file)
                logger.info(f"Updated Tabasco backend: {backend_file}")
            else:
                logger.warning(f"Source file not found: {yaml_file}")
                
        except Exception as e:
            logger.error(f"Failed to update Tabasco backend: {e}")
    
    def _convert_to_tabasco_format(self, source_file: str, target_file: str):
        """Convert scraper YAML format to Tabasco format."""
        try:
            with open(source_file, 'r') as f:
                scraper_data = yaml.safe_load(f)
            
            tabasco_data = {}
            services = scraper_data.get('genai_pricing_data', {}).get('services', {})
            
            for service_key, service_data in services.items():
                if service_data.get('status') == 'success':
                    pricing_plans = service_data.get('pricing_plans', {})
                    
                    # Get the main pricing plan (usually 'business' or 'pro')
                    main_plan = None
                    plan_priority = ['business', 'pro', 'plus', 'standard', 'premium']
                    
                    for priority_plan in plan_priority:
                        if priority_plan in pricing_plans:
                            main_plan = pricing_plans[priority_plan]
                            break
                    
                    if not main_plan and pricing_plans:
                        # Use first available plan
                        main_plan = list(pricing_plans.values())[0]
                    
                    if main_plan:
                        # Get service config for additional metadata
                        service_config = SERVICES_CONFIG.get(service_key, {})
                        
                        tool_key = service_key.replace('_', '-').title()
                        tabasco_data[tool_key] = {
                            'name': service_data.get('name', ''),
                            'provider': service_config.get('provider', 'Unknown'),
                            'category': service_config.get('category', 'General AI'),
                            'price_per_license_usd_monthly': main_plan.get('price', 0),
                            'features': [],  # Could be enhanced with scraped features
                            'description': f"AI tool from {service_config.get('provider', 'Unknown')}",
                            'last_updated': service_data.get('last_updated', ''),
                            'data_source': service_data.get('data_source', 'web_scraping')
                        }
            
            # Write to Tabasco format
            with open(target_file, 'w') as f:
                yaml.dump(tabasco_data, f, default_flow_style=False, sort_keys=False, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to convert format: {e}")
    
    def run_scraping_cycle(self):
        """Run a complete scraping cycle."""
        logger.info("=" * 60)
        logger.info("Starting automated scraping cycle")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        self.stats['total_runs'] += 1
        self.stats['last_run'] = start_time.isoformat()
        
        try:
            # Create backup before scraping
            self._create_backup()
            
            # Run scraper
            logger.info(f"Scraping {len(SERVICES_CONFIG)} AI tool services...")
            data = self.scraper.scrape_all_services()
            
            # Validate data
            if self._validate_pricing_data(data):
                # Publish data
                logger.info("Publishing pricing data...")
                published_files = self.publisher.publish_all_formats(data)
                
                # Update Tabasco backend
                self._update_tabasco_backend()
                
                # Update stats
                self.stats['successful_runs'] += 1
                self.stats['last_successful_run'] = start_time.isoformat()
                
                # Update service success rates
                services = data.get('services', {})
                for service_key, service_data in services.items():
                    if service_key not in self.stats['services_success_rate']:
                        self.stats['services_success_rate'][service_key] = {'success': 0, 'total': 0}
                    
                    self.stats['services_success_rate'][service_key]['total'] += 1
                    if service_data.get('status') == 'success':
                        self.stats['services_success_rate'][service_key]['success'] += 1
                
                logger.info("✅ Scraping cycle completed successfully")
                
                # Log published files
                for format_type, filepath in published_files.items():
                    if filepath:
                        logger.info(f"📄 {format_type.upper()}: {filepath}")
                
            else:
                logger.error("❌ Data validation failed - not publishing")
                self.stats['errors'].append({
                    'timestamp': start_time.isoformat(),
                    'error': 'Data validation failed'
                })
        
        except Exception as e:
            logger.error(f"❌ Scraping cycle failed: {e}")
            self.stats['errors'].append({
                'timestamp': start_time.isoformat(),
                'error': str(e)
            })
        
        finally:
            # Cleanup and save stats
            self.scraper.cleanup()
            
            # Keep only last 10 errors
            self.stats['errors'] = self.stats['errors'][-10:]
            self._save_stats()
            
            duration = datetime.now() - start_time
            logger.info(f"Cycle completed in {duration.total_seconds():.1f} seconds")
            logger.info("=" * 60)
    
    def print_stats(self):
        """Print current scraper statistics."""
        logger.info("📊 Scraper Statistics:")
        logger.info(f"   Total runs: {self.stats['total_runs']}")
        logger.info(f"   Successful runs: {self.stats['successful_runs']}")
        
        if self.stats['total_runs'] > 0:
            success_rate = (self.stats['successful_runs'] / self.stats['total_runs']) * 100
            logger.info(f"   Success rate: {success_rate:.1f}%")
        
        if self.stats['last_successful_run']:
            last_success = datetime.fromisoformat(self.stats['last_successful_run'])
            time_since = datetime.now() - last_success
            logger.info(f"   Last successful run: {time_since.total_seconds() / 3600:.1f} hours ago")
        
        # Service-specific stats
        for service_key, service_stats in self.stats['services_success_rate'].items():
            if service_stats['total'] > 0:
                rate = (service_stats['success'] / service_stats['total']) * 100
                service_name = SERVICES_CONFIG.get(service_key, {}).get('name', service_key)
                logger.info(f"   {service_name}: {rate:.1f}% ({service_stats['success']}/{service_stats['total']})")

def main():
    """Main function to run automated scraper."""
    logger.info("🌶️ Starting Tabasco Automated Scraper")
    
    scraper = AutomatedScraper()
    
    # Print initial stats
    scraper.print_stats()
    
    # Schedule scraping every 5 hours
    schedule.every(5).hours.do(scraper.run_scraping_cycle)
    
    # Run immediately on startup
    logger.info("Running initial scraping cycle...")
    scraper.run_scraping_cycle()
    
    logger.info("🕐 Scheduler started - scraping every 5 hours")
    logger.info("Press Ctrl+C to stop")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    except KeyboardInterrupt:
        logger.info("🛑 Scraper stopped by user")
    except Exception as e:
        logger.error(f"💥 Scraper crashed: {e}")
    finally:
        scraper.scraper.cleanup()

if __name__ == "__main__":
    main()
