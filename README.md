# 🌶️ Tabasco - GenAI ROI Calculator

A comprehensive platform for calculating and visualizing Return on Investment (ROI) for Generative AI tools in enterprise environments.

## 🚀 Project Overview

**Tabasco** is a modern web application that helps organizations:
- **Calculate ROI** for GenAI tool investments
- **Visualize productivity gains** across different employee levels
- **Make data-driven decisions** about AI tool adoption
- **Track performance** with beautiful, interactive dashboards

## 📁 Project Structure

```
PriceCollector/
├── 🌶️ tabasco-backend/     # Flask API server
├── 🎨 tabasco-frontend/    # React web application
├── 🔍 price-scraper/       # Original price scraping tool
└── 📚 README.md            # This file
```

## 🎯 Quick Start

### 1. Backend Setup
```bash
cd tabasco-backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python create_user.py
python app.py
```
Backend runs on: `http://localhost:5004`

### 2. Frontend Setup
```bash
cd tabasco-frontend
npm install
npm start
```
Frontend runs on: `http://localhost:3000`

### 3. Login
- **Email:** `<EMAIL>`
- **Password:** `password`

## ✨ Features

### 🎨 Modern UI
- **Beautiful shadcn/ui components** with gradient backgrounds
- **Interactive tool selection** with switches and checkboxes
- **Real-time calculations** and cost tracking
- **Responsive design** for all devices
- **Professional animations** and hover effects

### 📊 ROI Analysis
- **Multi-tool comparison** across different AI platforms
- **Employee level targeting** (junior, senior, executive)
- **Interactive charts** and visualizations
- **Export capabilities** for reports and presentations

### 🔐 Security
- **User authentication** with session management
- **Protected routes** and secure API endpoints
- **CORS configuration** for cross-origin requests

## 🛠️ Technology Stack

### Backend
- **Flask** - Python web framework
- **SQLite** - Database for user and configuration data
- **Flask-Login** - User session management
- **CORS** - Cross-origin resource sharing

### Frontend
- **React 19** - Modern JavaScript framework
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **shadcn/ui** - Beautiful component library
- **Radix UI** - Accessible primitives
- **Lucide React** - Modern icons
- **Recharts** - Interactive data visualization

## 📈 ROI Calculation

The platform calculates ROI based on:
- **Tool costs** (monthly licensing fees)
- **Employee productivity gains** by level
- **Time savings** and efficiency improvements
- **Net benefit analysis** over time periods

## 🎯 Use Cases

- **Enterprise AI adoption** planning
- **Budget justification** for AI tools
- **Performance tracking** of AI investments
- **Comparative analysis** of different AI platforms
- **Executive reporting** and decision support

## 🔍 Price Scraper (Legacy)

The original price scraping functionality has been moved to `price-scraper/` for better organization.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with modern web technologies
- Inspired by enterprise SaaS applications
- Designed for real-world ROI analysis needs