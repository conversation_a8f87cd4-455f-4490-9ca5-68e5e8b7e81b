{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h2>{{ title }}</h2>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    {{ form.hidden_tag() }}
                    <div class="form-group">
                        {{ form.email.label(class="form-control-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control form-control-lg is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control form-control-lg") }}
                        {% endif %}
                    </div>
                    <div class="form-group">
                        {{ form.password.label(class="form-control-label") }}
                        {% if form.password.errors %}
                            {{ form.password(class="form-control form-control-lg is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.password(class="form-control form-control-lg") }}
                        {% endif %}
                    </div>
                    <div class="form-group form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary btn-lg btn-block") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-muted">
                Need an account? <a href="{{ url_for('auth.register') }}">Sign Up Now</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
