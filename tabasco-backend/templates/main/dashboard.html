{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1>{{ title }}</h1>
    <p>Welcome, {{ current_user.username }}!</p>

    <!-- Section for User's Employees -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>Your Employees</h3>
        </div>
        <div class="card-body">
            {% if user_employees %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Employee ID</th>
                            <th>Level</th>
                            <th>Tenure in Role (Yrs)</th>
                            <th>Tenure Since Hire (Yrs)</th>
                            <th>Total Compensation (USD)</th>
                            <!-- <th>Actions</th> -->
                        </tr>
                    </thead>
                    <tbody>
                        {% for emp in user_employees %}
                        <tr>
                            <td>{{ emp.employee_id_str }}</td>
                            <td>{{ emp.level }}</td>
                            <td>{{ emp.tenure_in_role }}</td>
                            <td>{{ emp.tenure_since_last_hire }}</td>
                            <td>${{ "{:,.2f}".format(emp.total_compensation) }}</td>
                            <!-- <td><a href="#" class="btn btn-sm btn-info">Edit</a> <a href="#" class="btn btn-sm btn-danger">Delete</a></td> -->
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>You haven't added any employees yet. <a href="{{ url_for('main.add_employee') }}" class="btn btn-primary">Add Employee</a></p>
            {% endif %}
            {% if user_employees|length < 30 %} <!-- Or some other limit -->
             <a href="{{ url_for('main.add_employee') }}" class="btn btn-success mt-3">Add New Employee</a>
            {% endif %}
        </div>
    </div>

    <!-- Section for Randomly Generated Employee Table -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>Market Sample: Software Dev Engineers (Random Data)</h3>
        </div>
        <div class="card-body">
            <p>This table shows randomly generated data for 30 SDEs for market comparison.</p>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Employee ID</th>
                            <th>Level</th>
                            <th>Tenure in Role (Yrs)</th>
                            <th>Tenure Since Hire (Yrs)</th>
                            <th>Total Compensation (USD)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for emp in random_employees_table %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ emp.employee_id }}</td>
                            <td>{{ emp.level }}</td>
                            <td>{{ emp.tenure_in_role }}</td>
                            <td>{{ emp.tenure_since_last_hire }}</td>
                            <td>${{ "{:,.2f}".format(emp.total_compensation) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Section for GenAI Tool Pricing -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>GenAI Tool Pricing (from genai_tool_pricing.yaml)</h3>
        </div>
        <div class="card-body">
            {% if genai_tools_pricing %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Tool Name</th>
                            <th>Monthly Price (USD)</th>
                            <th>Features</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tool_name, details in genai_tools_pricing.items() %}
                        <tr>
                            <td>{{ details.name }}</td>
                            <td>${{ "{:,.2f}".format(details.price_per_license_usd_monthly) }}</td>
                            <td>
                                <ul>
                                {% for feature in details.features %}
                                    <li>{{ feature }}</li>
                                {% endfor %}
                                </ul>
                            </td>
                            <td>{{ details.description }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No GenAI tool pricing information found. Please check <code>genai_tool_pricing.yaml</code>.</p>
            {% endif %}
        </div>
    </div>

    <!-- Section for Manual Productivity Gains -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>Manual Productivity Gain Estimates (from manual_productivity_gains.yaml)</h3>
        </div>
        <div class="card-body">
            <!-- Add a form here later to allow users to update these values directly -->
            {% if manual_gains %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Level</th>
                            <th>Role</th>
                            <th>Productivity Gain (%)</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for level, details in manual_gains.items() %}
                        <tr>
                            <td>{{ level }}</td>
                            <td>{{ details.role }}</td>
                            <td>{{ details.productivity_gain_percentage }}%</td>
                            <td>{{ details.notes }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <p><small>You can edit these values in <code>manual_productivity_gains.yaml</code>.</small></p>
            {% else %}
            <p>No manual productivity gain information found. Please check <code>manual_productivity_gains.yaml</code>.</p>
            {% endif %}
        </div>
    </div>

    <!-- Interactive ROI Configuration Form -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>Configure ROI Calculation</h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('main.dashboard') }}">
                <h5>Select Tools and Specify License Counts:</h5>
                <div class="table-responsive mb-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Include</th>
                                <th>Tool Name</th>
                                <th>Licenses (for cost)</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for tool_key, details in genai_tools_pricing.items() %}
                            <tr>
                                <td>
                                    <input type="checkbox" name="tool_{{ tool_key }}" value="selected" 
                                           {% if request.args.get('tool_' + tool_key) == 'selected' or not request.args %}checked{% endif %}>
                                    {# By default, check all if no args present (initial load) #}
                                </td>
                                <td>{{ details.name }}</td>
                                <td>
                                    <input type="number" name="licenses_{{ tool_key }}" 
                                           value="{{ request.args.get('licenses_' + tool_key, 30) }}" 
                                           class="form-control form-control-sm" style="width: 100px;" min="0">
                                    {# Default to 30 licenses (number of sample employees) #}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>

                <hr>
                <h5>Filter Market Sample Employees by Level (for Savings Calculation):</h5>
                <div class="form-row mb-3">
                    {% set all_levels = ['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7', 'L8', 'L9', 'L10'] %}
                    {% for level in all_levels %}
                    <div class="form-group col-auto">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="level_{{ level }}" id="level_{{ level }}" value="selected"
                                   {% if request.args.get('level_' + level) == 'selected' or not request.args %}checked{% endif %}>
                            <label class="form-check-label" for="level_{{ level }}">{{ level }}</label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <button type="submit" class="btn btn-primary">Recalculate ROI</button>
            </form>
        </div>
    </div>

    <!-- ROI Calculation and Visualization Section -->
    <div class="dashboard-section card">
        <div class="card-header">
            <h3>GenAI Investment ROI Analysis</h3>
        </div>
        <div class="card-body">
            {% if savings_data %}
                <h4>Potential ROI per Tool (Based on Market Sample Employees & Manual Gains)</h4>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>GenAI Tool</th>
                                <th>Total Annual Cost (USD)</th>
                                <th>Total Annual Potential Savings (USD)</th>
                                <th>Net ROI (USD)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in savings_data %}
                            <tr>
                                <td>{{ item.tool_name }}</td>
                                <td>${{ "{:,.2f}".format(item.total_annual_cost) }}</td>
                                <td>${{ "{:,.2f}".format(item.total_annual_potential_savings) }}</td>
                                <td class="{{ 'text-success' if item.net_roi > 0 else 'text-danger' if item.net_roi < 0 else '' }}">
                                    ${{ "{:,.2f}".format(item.net_roi) }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Chart.js Canvas -->
                <div style="width: 80%; margin: auto; padding-top: 20px;">
                    <canvas id="roiChart"></canvas>
                </div>
            {% elif not user_employees %}
                <p>Please <a href="{{ url_for('main.add_employee') }}">add your employees</a> to see ROI calculations.</p>
            {% elif not genai_tools_pricing %}
                <p>GenAI tool pricing is missing. Please check <code>genai_tool_pricing.yaml</code>.</p>
            {% elif not manual_gains %}
                 <p>Manual productivity gains are missing. Please check <code>manual_productivity_gains.yaml</code>.</p>
            {% else %}
                <p>Unable to calculate ROI. Ensure you have employees, tool pricing, and productivity gains defined.</p>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const savingsData = {{ savings_data | tojson | safe }};
    if (savingsData && savingsData.length > 0) {
        const toolNames = savingsData.map(item => item.tool_name);
        const totalCosts = savingsData.map(item => item.total_annual_cost);
        const totalSavings = savingsData.map(item => item.total_annual_potential_savings);
        const netRoi = savingsData.map(item => item.net_roi);

        const ctx = document.getElementById('roiChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar', // or 'line', 'pie', etc.
            data: {
                labels: toolNames,
                datasets: [
                    {
                        label: 'Total Annual Cost (USD)',
                        data: totalCosts,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)', // Reddish
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Total Annual Potential Savings (USD)',
                        data: totalSavings,
                        backgroundColor: 'rgba(75, 192, 192, 0.5)', // Greenish
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Net ROI (USD)',
                        data: netRoi,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)', // Bluish
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false, // Allow negative ROI to be shown appropriately
                        ticks: {
                            callback: function(value, index, values) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        ticks: {
                            autoSkip: false,
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += '$' + context.parsed.y.toLocaleString();
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}
