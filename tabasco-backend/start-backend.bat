@echo off
REM 🌶️ Tabasco Backend Startup Script for Windows
REM This script sets up and starts the Flask backend server

echo 🌶️  Starting Tabasco Backend...
echo ==================================

REM Check if we're in the right directory
if not exist "app.py" (
    echo [ERROR] app.py not found. Please run this script from the tabasco-backend directory.
    pause
    exit /b 1
)

REM 1. Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "instance" mkdir instance
if not exist "logs" mkdir logs
echo [SUCCESS] Directories created

REM 2. Check if .env file exists, create from example if not
if not exist ".env" (
    echo [WARNING] .env file not found
    if exist ".env.example" (
        echo [INFO] Copying .env.example to .env...
        copy ".env.example" ".env"
        echo [SUCCESS] .env file created from example
    ) else (
        echo [ERROR] .env.example not found. Please create .env file manually.
        pause
        exit /b 1
    )
)

REM 3. Install/upgrade dependencies
echo [INFO] Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed

REM 4. Create default users (demo and admin) if they don't exist
echo [INFO] Setting up database and default users...
if exist "create_default_users.py" (
    python create_default_users.py
    echo [SUCCESS] Default users setup completed
) else if exist "create_demo_user.py" (
    echo [WARNING] Using legacy demo user creation...
    python create_demo_user.py
    echo [SUCCESS] Demo user setup completed
) else (
    echo [WARNING] No user creation script found, skipping user setup
)

REM 5. Start the Flask application
echo [INFO] Starting Flask backend server...
echo.
echo [SUCCESS] 🚀 Backend starting on http://localhost:5004
echo [SUCCESS] 📊 Default login credentials:
echo [SUCCESS]    👤 Demo User: demo / demo123
echo [SUCCESS]    🔐 Admin User: admin / admin123
echo.
echo [INFO] Press Ctrl+C to stop the server
echo ==================================

REM Start the application
python app.py

pause
