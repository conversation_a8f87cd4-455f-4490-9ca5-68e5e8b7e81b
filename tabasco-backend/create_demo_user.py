#!/usr/bin/env python3
"""
Simple script to create a demo user for testing
"""

from app import create_app
from extensions import db
from models import User
from werkzeug.security import generate_password_hash

def create_demo_user():
    """Create a demo user with bcrypt hashing"""
    app = create_app()

    with app.app_context():
        try:
            # Check if demo user already exists and delete it
            user = User.query.filter_by(username='demo').first()
            if user:
                print("👤 Demo user exists, deleting and recreating...")
                db.session.delete(user)
                db.session.commit()

            # Create password hash using Werkzeug with pbkdf2 method
            password = 'demo123'
            password_hash = generate_password_hash(password, method='pbkdf2:sha256')

            # Create demo user
            user = User(
                username='demo',
                email='<EMAIL>',
                password_hash=password_hash
            )

            db.session.add(user)
            db.session.commit()

            print("✅ Created demo user successfully!")
            print("📊 Login credentials:")
            print("   Username: demo")
            print("   Password: demo123")

            return True

        except Exception as e:
            print(f"❌ Error creating demo user: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = create_demo_user()
    if not success:
        print("💥 Failed to create demo user!")
