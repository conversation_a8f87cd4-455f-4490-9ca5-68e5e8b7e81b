#!/usr/bin/env python3
"""
Complete setup script for Tabasco AI Tools Management
Initializes database and populates with sample data
"""

import sys
import os
from init_db import init_database
from populate_sample_data import populate_sample_data

def main():
    """Main setup function"""
    print("🌶️  Welcome to Tabasco AI Tools Management Setup!")
    print("=" * 50)
    
    # Step 1: Initialize Database
    print("\n📊 Step 1: Initializing Database...")
    if not init_database():
        print("❌ Database initialization failed!")
        return False
    
    # Step 2: Populate Sample Data
    print("\n📝 Step 2: Populating Sample Data...")
    if not populate_sample_data():
        print("❌ Sample data population failed!")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Tabasco Setup Complete!")
    print("\n📋 What's been set up:")
    print("   ✅ Database tables created")
    print("   ✅ Sample user created (demo/demo123)")
    print("   ✅ 10 sample employees added")
    print("   ✅ 3 sample AI tools added")
    print("\n🚀 Next Steps:")
    print("   1. Start the Flask backend: python app.py")
    print("   2. Start the Next.js frontend: npm run dev")
    print("   3. Login with demo/demo123")
    print("   4. Explore the AI Tools Management features!")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 Setup failed! Please check the error messages above.")
        sys.exit(1)
    else:
        print("\n🌶️  Enjoy using Tabasco!")
