#!/usr/bin/env python3
"""
Test script for Editable Salary functionality
"""

import requests
import json

BASE_URL = 'http://localhost:5004'

def test_salary_update():
    """Test updating job family salary"""
    print("🧪 Testing Salary Update API")
    print("=" * 50)
    
    # Test data
    test_cases = [
        {
            'job_family': 'Software Engineer',
            'new_salary': 185000,
            'description': 'Increase Software Engineer salary to $185K'
        },
        {
            'job_family': 'Product Manager',
            'new_salary': 165000,
            'description': 'Increase Product Manager salary to $165K'
        },
        {
            'job_family': 'Data Scientist',
            'new_salary': 195000,
            'description': 'Increase Data Scientist salary to $195K'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 Test Case {i}: {test_case['description']}")
        print("-" * 40)
        
        payload = {
            'job_family': test_case['job_family'],
            'new_avg_salary': test_case['new_salary']
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-salary",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Successfully updated {test_case['job_family']} salary!")
                print(f"📊 Old Average: ${result['old_avg_salary']:,.0f}")
                print(f"📊 New Average: ${result['new_avg_salary']:,.0f}")
                print(f"📊 Target Average: ${result['target_avg_salary']:,.0f}")
                print(f"📊 Adjustment Factor: {result['adjustment_factor']:.3f}")
                print(f"👥 Employees Updated: {result['employee_count']}")
                print(f"💰 New ROI: {result['new_roi']}")
                print(f"💰 New Savings: {result['new_savings']}")
                
                # Show some employee details
                print(f"\n📋 Sample Employee Updates:")
                for emp in result['updated_employees'][:3]:  # Show first 3
                    print(f"   • {emp['employee_id']} ({emp['level']}): ${emp['old_salary']:,} → ${emp['new_salary']:,}")
                
                if len(result['updated_employees']) > 3:
                    print(f"   ... and {len(result['updated_employees']) - 3} more employees")
                    
            else:
                print(f"❌ Failed to update salary: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing salary update: {e}")

def test_invalid_salary_updates():
    """Test invalid salary update scenarios"""
    print("\n\n🧪 Testing Invalid Salary Updates")
    print("=" * 50)
    
    invalid_cases = [
        {
            'job_family': 'Software Engineer',
            'new_salary': 0,
            'description': 'Zero salary (should fail)'
        },
        {
            'job_family': 'Software Engineer',
            'new_salary': -50000,
            'description': 'Negative salary (should fail)'
        },
        {
            'job_family': 'Nonexistent Role',
            'new_salary': 100000,
            'description': 'Nonexistent job family (should fail)'
        },
        {
            'job_family': '',
            'new_salary': 100000,
            'description': 'Empty job family (should fail)'
        }
    ]
    
    for i, test_case in enumerate(invalid_cases, 1):
        print(f"\n🔄 Invalid Test {i}: {test_case['description']}")
        print("-" * 40)
        
        payload = {
            'job_family': test_case['job_family'],
            'new_avg_salary': test_case['new_salary']
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-salary",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code != 200:
                print(f"✅ Correctly rejected invalid request: {response.status_code}")
                error_data = response.json()
                print(f"📝 Error message: {error_data.get('error', 'Unknown error')}")
            else:
                print(f"❌ Unexpectedly accepted invalid request")
                print(f"Response: {response.json()}")
                
        except Exception as e:
            print(f"❌ Error testing invalid salary: {e}")

def test_employee_management_data():
    """Test the employee management data after salary updates"""
    print("\n\n🧪 Testing Employee Management Data After Updates")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/employee-management")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully fetched updated employee data!")
            
            print(f"\n📊 Updated Job Families:")
            for family in result['job_families']:
                print(f"   • {family['name']}: {family['count']} employees")
                print(f"     💰 Avg Salary: {family['avgSalary']}")
                print(f"     📈 ROI: {family['roi']}")
                if 'savings' in family:
                    print(f"     💵 Savings: {family['savings']}")
                print()
        else:
            print(f"❌ Failed to get employee data: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing employee data: {e}")

def test_roi_impact():
    """Test ROI impact of salary changes"""
    print("\n\n🧪 Testing ROI Impact of Salary Changes")
    print("=" * 50)
    
    # Test different salary levels for the same job family
    salary_levels = [120000, 150000, 180000, 220000]
    job_family = 'Software Engineer'
    
    print(f"📊 Testing ROI impact for {job_family} at different salary levels:")
    print("-" * 60)
    
    for salary in salary_levels:
        payload = {
            'job_family': job_family,
            'new_avg_salary': salary
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-salary",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"💰 Salary: ${salary:,} → ROI: {result['new_roi']} → Savings: {result['new_savings']}")
            else:
                print(f"❌ Failed at ${salary:,}: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error at ${salary:,}: {e}")

if __name__ == '__main__':
    print("🚀 Testing Editable Salary Functionality")
    print("=" * 60)
    
    # Run all tests
    test_salary_update()
    test_invalid_salary_updates()
    test_employee_management_data()
    test_roi_impact()
    
    print("\n✅ All salary update tests completed!")
