#!/usr/bin/env python3
"""
<PERSON>ript to create sample employees in the database
"""

import random
from app import create_app
from models import User, Employee, db

def create_sample_employees():
    app = create_app()

    with app.app_context():
        # Get the admin user (assuming it exists)
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Admin user not found. Please create an admin user first.")
            return

        # Clear existing employees for this user
        Employee.query.filter_by(user_id=admin_user.id).delete()

        # Job families and their typical levels
        job_families = [
            {'name': 'Software Engineer', 'levels': ['L3', 'L4', 'L5', 'L6', 'L7'], 'base_salary': 150000, 'count': 8},
            {'name': 'Product Manager', 'levels': ['L5', 'L6', 'L7', 'L8'], 'base_salary': 180000, 'count': 4},
            {'name': 'Data Scientist', 'levels': ['L4', 'L5', 'L6', 'L7'], 'base_salary': 170000, 'count': 6},
            {'name': 'Designer', 'levels': ['L3', 'L4', 'L5', 'L6'], 'base_salary': 130000, 'count': 4},
            {'name': 'Research Scientist', 'levels': ['L5', 'L6', 'L7', 'L8'], 'base_salary': 190000, 'count': 3},
            {'name': 'Engineering Manager', 'levels': ['L6', 'L7', 'L8'], 'base_salary': 200000, 'count': 3},
            {'name': 'DevOps Engineer', 'levels': ['L4', 'L5', 'L6'], 'base_salary': 160000, 'count': 2}
        ]

        employee_id_counter = 1000

        for job_family in job_families:
            for i in range(job_family['count']):
                level = random.choice(job_family['levels'])

                # Calculate salary based on level and job family
                level_multiplier = {
                    'L3': 0.8, 'L4': 1.0, 'L5': 1.3, 'L6': 1.6,
                    'L7': 2.0, 'L8': 2.5, 'L9': 3.0, 'L10': 3.5
                }

                base_salary = job_family['base_salary']
                salary = int(base_salary * level_multiplier.get(level, 1.0) * random.uniform(0.9, 1.1))

                employee = Employee(
                    employee_id_str=f"{job_family['name'][:3].upper()}{employee_id_counter}",
                    job_family=job_family['name'],
                    tenure_in_role=random.randint(1, 5),
                    level=level,
                    tenure_since_last_hire=random.randint(1, 8),
                    total_compensation=salary,
                    user_id=admin_user.id
                )

                db.session.add(employee)
                employee_id_counter += 1

        db.session.commit()

        # Count total employees created
        total_employees = Employee.query.filter_by(user_id=admin_user.id).count()
        print(f"Successfully created {total_employees} sample employees for user '{admin_user.username}'")

        # Show some statistics
        employees = Employee.query.filter_by(user_id=admin_user.id).all()
        total_compensation = sum(emp.total_compensation for emp in employees)
        avg_compensation = total_compensation / len(employees) if employees else 0

        print(f"Total compensation: ${total_compensation:,}")
        print(f"Average compensation: ${avg_compensation:,.0f}")

        # Show level distribution
        level_counts = {}
        for emp in employees:
            level_counts[emp.level] = level_counts.get(emp.level, 0) + 1

        print("\nLevel distribution:")
        for level, count in sorted(level_counts.items()):
            print(f"  {level}: {count} employees")

if __name__ == '__main__':
    create_sample_employees()
