#!/usr/bin/env python3
"""
Test script to verify ROI calculations with real costs and benefits
"""

import yaml
from app import create_app
from models import Employee, User

def test_roi_calculation():
    app = create_app()
    
    with app.app_context():
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Admin user not found")
            return
        
        employees = Employee.query.filter_by(user_id=admin_user.id).all()
        if not employees:
            print("No employees found")
            return
        
        # Load ROI configuration
        with open('roi_config.yaml', 'r') as f:
            roi_config = yaml.safe_load(f)
        
        print("ROI Calculation Test")
        print("=" * 60)
        
        # Group employees by job family
        from collections import defaultdict
        job_family_data = defaultdict(list)
        for emp in employees:
            job_family_data[emp.job_family].append(emp)
        
        # Level weights for salary calculation
        level_weights = {
            'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3, 
            'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8
        }
        
        ai_tool_costs = roi_config.get('ai_tool_costs', {})
        global_settings = roi_config.get('global_settings', {})
        productivity_gain = global_settings.get('productivity_gain_base', 15) / 100
        working_days = global_settings.get('working_days_per_month', 22)
        hours_per_day = global_settings.get('hours_per_day', 8)
        
        print(f"Global Settings:")
        print(f"  Productivity Gain: {productivity_gain*100:.0f}%")
        print(f"  Working Days/Month: {working_days}")
        print(f"  Hours/Day: {hours_per_day}")
        print()
        
        for job_family, emp_list in job_family_data.items():
            count = len(emp_list)
            
            # Calculate weighted average salary
            family_weighted_compensation = 0
            family_total_weights = 0
            
            for emp in emp_list:
                weight = level_weights.get(emp.level, 1)
                family_weighted_compensation += emp.total_compensation * weight
                family_total_weights += weight
            
            avg_salary = family_weighted_compensation / family_total_weights if family_total_weights > 0 else 0
            
            # Get adoption rate
            family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
            ai_adoption = family_config.get('base_ai_adoption', 70)
            adoption_rate = ai_adoption / 100
            
            # Get costs
            monthly_cost_per_user = ai_tool_costs.get(job_family, 50)
            monthly_ai_cost = count * monthly_cost_per_user
            
            # Calculate benefits
            hourly_salary = avg_salary / (12 * working_days * hours_per_day)
            adopted_employees = count * adoption_rate
            monthly_productivity_value = adopted_employees * productivity_gain * hourly_salary * working_days * hours_per_day
            
            # Calculate ROI
            if monthly_ai_cost > 0:
                roi_percentage = ((monthly_productivity_value - monthly_ai_cost) / monthly_ai_cost) * 100
            else:
                roi_percentage = 0
            
            if adoption_rate == 0:
                roi_percentage = -100
            
            print(f"{job_family}:")
            print(f"  Employees: {count}")
            print(f"  Avg Salary: ${avg_salary:,.0f}")
            print(f"  Hourly Rate: ${hourly_salary:.2f}")
            print(f"  AI Adoption: {ai_adoption}% ({adopted_employees:.1f} users)")
            print(f"  Monthly AI Cost: ${monthly_ai_cost:,.0f} (${monthly_cost_per_user}/user)")
            print(f"  Monthly Productivity Value: ${monthly_productivity_value:,.0f}")
            print(f"  Net Monthly Benefit: ${monthly_productivity_value - monthly_ai_cost:,.0f}")
            print(f"  ROI: {roi_percentage:+.0f}%")
            print()
        
        print("ROI Calculation Logic:")
        print("- Monthly Cost = Employees × Cost per User")
        print("- Monthly Benefit = Adopted Users × Productivity Gain × Hourly Salary × Working Hours")
        print("- ROI = (Benefits - Costs) / Costs × 100")
        print("- If Adoption = 0%, ROI = -100% (pure cost, no benefit)")

if __name__ == '__main__':
    test_roi_calculation()
