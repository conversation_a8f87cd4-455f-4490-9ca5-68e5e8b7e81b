from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user, login_user, logout_user
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.security import check_password_hash
import yaml
import random
import jwt
import datetime
import os
from functools import wraps
from extensions import db
from models import Employee, User

api_bp = Blueprint('api', __name__, url_prefix='/api')

# Get limiter instance (will be initialized by app)
from flask import current_app
def get_limiter():
    return getattr(current_app, 'limiter', None)

# JWT Token functions
def generate_token(user_id):
    """Generate JWT token for user"""
    payload = {
        'user_id': user_id,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
        'iat': datetime.datetime.utcnow()
    }
    secret_key = os.environ.get('SECRET_KEY', 'fallback-secret-key')
    return jwt.encode(payload, secret_key, algorithm='HS256')

def verify_token(token):
    """Verify JWT token and return user_id"""
    try:
        secret_key = os.environ.get('SECRET_KEY', 'fallback-secret-key')
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        return payload['user_id']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def token_required(f):
    """Decorator for routes that require JWT token authentication"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check for token in Authorization header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return jsonify({'error': 'Invalid token format'}), 401

        if not token:
            return jsonify({'error': 'Token is missing'}), 401

        user_id = verify_token(token)
        if user_id is None:
            return jsonify({'error': 'Token is invalid or expired'}), 401

        # Get user and set as current user for the request
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 401

        # Store user in request context
        request.current_user = user
        return f(*args, **kwargs)

    return decorated

@api_bp.route('/login', methods=['POST'])
def api_login():
    try:
        data = request.get_json()
        print(f"🔍 LOGIN ATTEMPT - Received data: {data}")

        username = data.get('username')
        password = data.get('password')
        print(f"🔍 LOGIN ATTEMPT - Username: '{username}', Password length: {len(password) if password else 0}")

        if not username or not password:
            print("❌ LOGIN FAILED - Missing username or password")
            return jsonify({'error': 'Username and password are required'}), 400

        # Try to find user by username or email
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        print(f"🔍 USER SEARCH - Found user: {user.username if user else 'None'}")
        if user:
            print(f"🔍 USER DETAILS - ID: {user.id}, Username: '{user.username}', Email: '{user.email}'")
            password_check = check_password_hash(user.password_hash, password)
            print(f"🔍 PASSWORD CHECK - Result: {password_check}")

        if user and check_password_hash(user.password_hash, password):
            # Generate JWT token
            token = generate_token(user.id)

            # Also login for session-based routes (backward compatibility)
            login_user(user, remember=True)

            print(f"✅ LOGIN SUCCESS - User {user.username} logged in successfully")
            print(f"🔍 JWT TOKEN - Generated token for user {user.id}")

            return jsonify({
                'message': 'Login successful',
                'token': token,  # JWT token for API authentication
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                }
            })
        else:
            print("❌ LOGIN FAILED - Invalid credentials")
            return jsonify({'error': 'Invalid credentials'}), 401
    except Exception as e:
        print(f"💥 LOGIN ERROR - Exception: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/logout', methods=['POST'])
@login_required
def api_logout():
    try:
        logout_user()
        return jsonify({'message': 'Logout successful'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_random_employees(num_employees=30):
    employees = []
    levels = [f"L{i}" for i in range(1, 11)]
    for i in range(1, num_employees + 1):
        level = random.choice(levels)
        employees.append({
            'id': i,
            'name': f'Employee {i}',
            'employee_id': f'SDE{1000+i}',
            'tenure_in_role': random.randint(1, 5),
            'level': level,
            'tenure_since_last_hire': random.randint(1, 10),
            'total_compensation': random.randint(200000, 600000),
        })
    return employees

@api_bp.route('/dashboard-data', methods=['GET'])
@login_required
def get_dashboard_data():
    try:
        # Get user's employees
        user_employees = Employee.query.filter_by(user_id=current_user.id).all()
        user_employees_data = [{
            'id': emp.id,
            'employee_id': emp.employee_id_str,
            'level': emp.level,
            'tenure_in_role': emp.tenure_in_role,
            'tenure_since_last_hire': emp.tenure_since_last_hire,
            'total_compensation': emp.total_compensation
        } for emp in user_employees]

        # Generate random employees table
        random_employees_table = generate_random_employees(30)

        # Load GenAI tool pricing from YAML
        try:
            with open('genai_tool_pricing.yaml', 'r') as f:
                genai_tools_pricing = yaml.safe_load(f)
        except FileNotFoundError:
            genai_tools_pricing = {}

        # Load manual productivity gains from YAML
        try:
            with open('manual_productivity_gains.yaml', 'r') as f:
                manual_gains = yaml.safe_load(f)
                if not isinstance(manual_gains, dict):
                    manual_gains = {}
        except FileNotFoundError:
            manual_gains = {}

        return jsonify({
            'user_employees': user_employees_data,
            'random_employees_table': random_employees_table,
            'genai_tools_pricing': genai_tools_pricing,
            'manual_gains': manual_gains,
            'user': {
                'id': current_user.id,
                'username': current_user.username,
                'email': current_user.email
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/calculate-roi', methods=['POST'])
@login_required
def calculate_roi():
    try:
        data = request.get_json()
        selected_tools = data.get('selected_tools', {})
        selected_levels = data.get('selected_levels', [])

        # Load data
        random_employees_table = generate_random_employees(30)

        try:
            with open('genai_tool_pricing.yaml', 'r') as f:
                genai_tools_pricing = yaml.safe_load(f)
        except FileNotFoundError:
            return jsonify({'error': 'GenAI tool pricing file not found'}), 400

        try:
            with open('manual_productivity_gains.yaml', 'r') as f:
                manual_gains = yaml.safe_load(f)
        except FileNotFoundError:
            return jsonify({'error': 'Manual productivity gains file not found'}), 400

        # Filter employees by selected levels
        if selected_levels:
            filtered_employees = [emp for emp in random_employees_table if emp.get('level') in selected_levels]
        else:
            filtered_employees = random_employees_table

        savings_data = []

        for tool_key, tool_config in selected_tools.items():
            if tool_key not in genai_tools_pricing:
                continue

            tool_info = genai_tools_pricing[tool_key]
            num_licenses = tool_config.get('licenses', 30)

            cost_per_license_monthly = tool_info.get('price_per_license_usd_monthly', 0)
            total_cost_for_tool = num_licenses * cost_per_license_monthly * 12

            total_potential_savings = 0

            for emp_data in filtered_employees:
                emp_level = emp_data.get('level')
                emp_compensation = emp_data.get('total_compensation')

                if emp_level and emp_compensation is not None:
                    productivity_gain_percentage = manual_gains.get(emp_level, {}).get('productivity_gain_percentage', 0) / 100.0
                    potential_savings_per_employee = emp_compensation * productivity_gain_percentage
                    total_potential_savings += potential_savings_per_employee

            net_roi = total_potential_savings - total_cost_for_tool
            roi_percentage = (net_roi / total_cost_for_tool * 100) if total_cost_for_tool > 0 else 0

            savings_data.append({
                'tool_name': tool_info.get('name', tool_key),
                'tool_key': tool_key,
                'total_annual_cost': total_cost_for_tool,
                'total_annual_potential_savings': total_potential_savings,
                'net_roi': net_roi,
                'roi_percentage': roi_percentage,
                'num_licenses': num_licenses,
                'affected_employees': len(filtered_employees)
            })

        return jsonify({
            'savings_data': savings_data,
            'total_employees_analyzed': len(filtered_employees),
            'selected_levels': selected_levels
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/employees', methods=['GET'])
@login_required
def get_employees():
    try:
        employees = Employee.query.filter_by(user_id=current_user.id).all()
        employees_data = [{
            'id': emp.id,
            'employee_id': emp.employee_id_str,
            'level': emp.level,
            'tenure_in_role': emp.tenure_in_role,
            'tenure_since_last_hire': emp.tenure_since_last_hire,
            'total_compensation': emp.total_compensation
        } for emp in employees]
        return jsonify(employees_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/employees', methods=['POST'])
@login_required
def add_employee():
    try:
        data = request.get_json()
        new_employee = Employee(
            employee_id_str=data['employee_id'],
            tenure_in_role=data['tenure_in_role'],
            level=data['level'],
            tenure_since_last_hire=data['tenure_since_last_hire'],
            total_compensation=data['total_compensation'],
            user_id=current_user.id
        )
        db.session.add(new_employee)
        db.session.commit()

        return jsonify({
            'id': new_employee.id,
            'employee_id': new_employee.employee_id_str,
            'level': new_employee.level,
            'tenure_in_role': new_employee.tenure_in_role,
            'tenure_since_last_hire': new_employee.tenure_since_last_hire,
            'total_compensation': new_employee.total_compensation
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/employees/<int:employee_id>', methods=['DELETE'])
@login_required
def delete_employee(employee_id):
    try:
        employee = Employee.query.filter_by(id=employee_id, user_id=current_user.id).first()
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        db.session.delete(employee)
        db.session.commit()
        return jsonify({'message': 'Employee deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/user', methods=['GET'])
@token_required
def get_current_user():
    """Get current user using JWT token authentication"""
    user = request.current_user
    print(f"🔍 JWT USER ENDPOINT - User: {user.username}")
    return jsonify({
        'id': user.id,
        'username': user.username,
        'email': user.email
    })

@api_bp.route('/user/session', methods=['GET'])
@login_required
def get_current_user_session():
    """Get current user using session authentication (fallback)"""
    print(f"🔍 SESSION USER ENDPOINT - Current user authenticated: {current_user.is_authenticated}")
    print(f"🔍 SESSION USER ENDPOINT - Session cookies: {request.cookies}")
    return jsonify({
        'id': current_user.id,
        'username': current_user.username,
        'email': current_user.email
    })
