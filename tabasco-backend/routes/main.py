from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
import yaml
import random
from extensions import db      # Import from extensions
from models import Employee
# from forms import EmployeeForm

main_bp = Blueprint('main', __name__)

# Dummy data for employees (replace with database interaction)
def generate_random_employees(num_employees=30):
    employees = []
    levels = [f"L{i}" for i in range(1, 11)]
    for i in range(1, num_employees + 1):
        level = random.choice(levels)
        employees.append({
            'id': i,
            'name': f'Employee {i}', # Added for better display
            'employee_id': f'SDE{1000+i}',
            'tenure_in_role': random.randint(1, 5), # years
            'level': level,
            'tenure_since_last_hire': random.randint(1, 10), # years
            'total_compensation': random.randint(200000, 600000),
        })
    return employees

@main_bp.route('/')
@main_bp.route('/dashboard')
@login_required
def dashboard():
    # User's employees (initially empty or from DB)
    user_employees = Employee.query.filter_by(user_id=current_user.id).all()

    # Randomly generated table of 30 employees
    random_employees_table = generate_random_employees(30)

    # Load GenAI tool pricing from YAML
    try:
        with open('genai_tool_pricing.yaml', 'r') as f:
            genai_tools_pricing = yaml.safe_load(f)
    except FileNotFoundError:
        genai_tools_pricing = {}
        flash('genai_tool_pricing.yaml not found. Please create it.', 'warning')
    except yaml.YAMLError:
        genai_tools_pricing = {}
        flash('Error parsing genai_tool_pricing.yaml.', 'danger')


    # Load manual productivity gain inputs from YAML
    try:
        with open('manual_productivity_gains.yaml', 'r') as f:
            manual_gains = yaml.safe_load(f)
            if not isinstance(manual_gains, dict): # Ensure it's a dictionary
                manual_gains = {}
                flash('manual_productivity_gains.yaml is not structured as a dictionary. Using empty gains.', 'warning')
    except FileNotFoundError:
        manual_gains = {} # Default to empty if file not found
        flash('manual_productivity_gains.yaml not found. Please create it or input gains manually.', 'info')
    except yaml.YAMLError:
        manual_gains = {}
        flash('Error parsing manual_productivity_gains.yaml.', 'danger')


    # --- Calculation Logic ---
    # This will be more complex and will involve user inputs for productivity gains per level
    # For now, let's pass the raw data to the template

    # Example: Calculate savings for user's employees if a tool is adopted
    # This is a placeholder for more detailed logic
    savings_data = []
    
    # Determine selected tools and their license counts from request arguments
    selected_tools_data = {}
    for tool_key, tool_details in genai_tools_pricing.items():
        # A tool is selected if its checkbox is present in args, or if no args are present (initial load, select all)
        is_selected = request.args.get(f'tool_{tool_key}') == 'selected'
        if not request.args and request.method == 'GET': # Initial load with no query parameters
            is_selected = True 
            
        if is_selected:
            try:
                licenses = int(request.args.get(f'licenses_{tool_key}', 30)) # Default to 30 if not specified
                if licenses < 0: licenses = 0 # Ensure non-negative
            except ValueError:
                licenses = 30 # Default on error
            selected_tools_data[tool_key] = {
                'details': tool_details,
                'licenses': licenses
            }

    # Calculate ROI based on the random_employees_table and selected tools/licenses
    if random_employees_table and selected_tools_data and manual_gains:
        for tool_key, tool_data in selected_tools_data.items():
            tool_info = tool_data['details']
            num_licenses = tool_data['licenses']
            
            cost_per_license_monthly = tool_info.get('price_per_license_usd_monthly', 0)
            total_cost_for_tool = num_licenses * cost_per_license_monthly * 12 # Annual cost for specified licenses

            total_potential_savings = 0
            # Savings are calculated based on all employees in the sample table,
            # assuming they all could benefit if the tool is adopted.
            # The "tool availability per level" feature would refine this.

            # Filter random_employees_table by selected levels for savings calculation
            all_employee_levels = [f'L{i}' for i in range(1, 11)]
            selected_levels_from_form = []
            
            # Check if any level filter is present in args. If not, and it's not initial load, it means user deselected all.
            # If it IS initial load (no args at all), all levels are considered selected.
            has_level_filter_args = any(f'level_{lvl}' in request.args for lvl in all_employee_levels)

            if not request.args and request.method == 'GET': # Initial load
                selected_levels_from_form = all_employee_levels.copy()
            elif has_level_filter_args: # Form submitted with some level selections (or deselections)
                for lvl in all_employee_levels:
                    if request.args.get(f'level_{lvl}') == 'selected':
                        selected_levels_from_form.append(lvl)
            # If form submitted and has_level_filter_args is false, selected_levels_from_form remains empty (user unchecked all)
            # However, if request.args is present but contains NO level filters (e.g. only tool filters), treat as all levels selected for now.
            # A more robust way: if any 'level_LX' is in request.args, then we honor selections. Otherwise, all levels.
            # This logic is tricky. The template defaults to checking all if no args.
            # So, if request.args is present, we must respect the level selections.
            # If request.args is empty, all levels are selected.

            if not request.args: # Initial load implies all levels selected
                 filtered_employee_sample = random_employees_table
            else: # Form was submitted, respect the selections
                # If no level_LX args are present AT ALL after form submission, it means user wants all levels (as they didn't uncheck anything specific to levels)
                # This needs to align with template's default checking.
                # The template checks all levels if `not request.args` OR if `request.args.get('level_LX')` is true.
                # So, if `request.args` is present, we only include explicitly checked levels.
                
                # Simpler logic: if form submitted, selected_levels_from_form holds the truth.
                # If initial load, all_employee_levels is the truth.
                
                # Corrected logic for selected levels based on form submission:
                active_level_filters = [lvl for lvl in all_employee_levels if f'level_{lvl}' in request.args]
                if not active_level_filters and request.args: 
                    # No level filters submitted, but other filters might be. Assume all levels if no level filters explicitly set.
                    # This case is ambiguous. Let's assume if any form field is submitted, level filters are also "active" even if all unchecked.
                    # The template logic `{% if request.args.get('level_' + level) == 'selected' or not request.args %}` means:
                    # - if `not request.args` (initial load): checked
                    # - if `request.args` present: checked only if `level_LX` is 'selected'
                    # So, if `request.args` is present, `selected_levels_from_form` will correctly capture only the checked ones.
                    # If `selected_levels_from_form` is empty after a form submission, it means user unchecked all levels.
                    pass # selected_levels_from_form is already built based on actual checks

                if not request.args: # Initial load
                    current_selected_levels = all_employee_levels
                else:
                    current_selected_levels = selected_levels_from_form

                if not current_selected_levels: # No levels selected by user
                    filtered_employee_sample = []
                else:
                    filtered_employee_sample = [emp for emp in random_employees_table if emp.get('level') in current_selected_levels]


            for emp_data in filtered_employee_sample: # Iterate over filtered sample
                emp_level = emp_data.get('level')
                emp_compensation = emp_data.get('total_compensation')

                if emp_level and emp_compensation is not None:
                    productivity_gain_percentage = manual_gains.get(emp_level, {}).get('productivity_gain_percentage', 0) / 100.0
                    potential_savings_per_employee = emp_compensation * productivity_gain_percentage
                    total_potential_savings += potential_savings_per_employee
            
            net_roi = total_potential_savings - total_cost_for_tool
            savings_data.append({
                'tool_name': tool_info.get('name', tool_key), # Use the display name
                'total_annual_cost': total_cost_for_tool,
                'total_annual_potential_savings': total_potential_savings,
                'net_roi': net_roi
            })


    return render_template('main/dashboard.html',
                           title='Dashboard',
                           user_employees=user_employees,
                           random_employees_table=random_employees_table,
                           genai_tools_pricing=genai_tools_pricing,
                           manual_gains=manual_gains,
                           savings_data=savings_data)

# Placeholder for adding/editing employees - to be developed
@main_bp.route('/add_employee', methods=['GET', 'POST'])
@login_required
def add_employee():
    # form = EmployeeForm()
    # if form.validate_on_submit():
    #     new_employee = Employee(
    #         employee_id=form.employee_id.data,
    #         tenure_in_role=form.tenure_in_role.data,
    #         level=form.level.data,
    #         tenure_since_last_hire=form.tenure_since_last_hire.data,
    #         total_compensation=form.total_compensation.data,
    #         user_id=current_user.id
    #     )
    #     db.session.add(new_employee)
    #     db.session.commit()
    #     flash('Employee added successfully!', 'success')
    #     return redirect(url_for('main.dashboard'))
    # return render_template('main/add_employee.html', title='Add Employee', form=form)
    flash('Add employee functionality is under development.', 'info')
    return redirect(url_for('main.dashboard'))
