from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from models import AITool, Employee, db
from datetime import datetime
import yaml
import os
from collections import defaultdict

advanced_api = Blueprint('advanced_api', __name__)

def calculate_tool_efficiency(tool):
    """Calculate efficiency rating for a tool based on category and usage patterns"""

    # Base efficiency rates by tool category
    category_efficiency = {
        'General AI Assistant': 18,      # ChatGPT, Claude - broad productivity gains
        'Code Generation': 35,           # GitHub Copilot - high developer productivity
        'Content Creation': 25,          # Jasper, Copy.ai - content workflow efficiency
        'Image Generation': 20,          # Midjourney, DALL-E - creative workflow
        'Writing Assistant': 15,         # Grammarly - writing quality and speed
        'Productivity': 22,              # Notion AI - workflow optimization
        'Data Analysis': 30,             # AI data tools - analysis speed
        'Customer Support': 28,          # AI chatbots - response efficiency
        'Custom Tool': 15                # Default for custom tools
    }

    # Get base efficiency for tool category
    base_efficiency = category_efficiency.get(tool.category, 15)

    # Adjust based on usage level
    usage_multipliers = {
        'low': 0.7,      # 70% of base efficiency
        'medium': 1.0,   # 100% of base efficiency
        'high': 1.3      # 130% of base efficiency
    }

    usage_multiplier = usage_multipliers.get(tool.usage_level, 1.0)

    # Calculate final efficiency
    calculated_efficiency = base_efficiency * usage_multiplier

    # Use user-defined efficiency if set, otherwise use calculated
    if tool.efficiency_rating > 0:
        return tool.efficiency_rating
    else:
        return round(calculated_efficiency, 1)

# User API
@advanced_api.route('/api/user', methods=['GET'])
@login_required
def get_current_user():
    """Get current user information"""
    return jsonify({
        'id': str(current_user.id),
        'username': current_user.username,
        'email': current_user.email
    })



# AI Tools Management
@advanced_api.route('/api/ai-tools', methods=['GET'])
@login_required
def get_ai_tools():
    """Get all AI tools for the current user with calculated metrics"""
    try:
        tools = AITool.query.filter_by(user_id=current_user.id).all()

        # Convert to dict and add calculated fields
        tools_data = []
        for tool in tools:
            tool_dict = tool.to_dict()

            # Add calculated total monthly cost (only for active tools)
            if tool.status == 'active':
                tool_dict['total_monthly_cost'] = tool.monthly_cost * tool.user_count
            else:
                tool_dict['total_monthly_cost'] = 0

            # Add formatted display values
            tool_dict['per_license_cost'] = tool.monthly_cost
            tool_dict['is_persisted'] = True  # Indicates this is saved data

            tools_data.append(tool_dict)

        return jsonify(tools_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/ai-tools', methods=['POST'])
@login_required
def create_ai_tool():
    """Create a new AI tool with proper validation and persistence"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['name', 'provider', 'category', 'monthly_cost']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Validate data types
        try:
            monthly_cost = float(data['monthly_cost'])
            user_count = int(data.get('user_count', 0))
            efficiency_rating = float(data.get('efficiency_rating', 0))
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid data types for numeric fields'}), 400

        # Create new tool with validated data
        tool = AITool(
            name=data['name'].strip(),
            provider=data['provider'].strip(),
            category=data['category'].strip(),
            monthly_cost=monthly_cost,
            usage_level=data.get('usage_level', 'medium'),
            efficiency_rating=efficiency_rating,
            user_count=user_count,
            status=data.get('status', 'active'),
            user_id=current_user.id
        )

        db.session.add(tool)
        db.session.commit()

        # Return tool with calculated fields
        tool_dict = tool.to_dict()
        tool_dict['total_monthly_cost'] = tool.monthly_cost * tool.user_count if tool.status == 'active' else 0
        tool_dict['per_license_cost'] = tool.monthly_cost
        tool_dict['is_persisted'] = True

        return jsonify(tool_dict), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to create tool: {str(e)}'}), 500

@advanced_api.route('/api/ai-tools/<int:tool_id>', methods=['PUT'])
@login_required
def update_ai_tool(tool_id):
    """Update an AI tool"""
    tool = AITool.query.filter_by(id=tool_id, user_id=current_user.id).first()
    if not tool:
        return jsonify({'error': 'Tool not found'}), 404

    data = request.get_json()
    tool.name = data.get('name', tool.name)
    tool.provider = data.get('provider', tool.provider)
    tool.category = data.get('category', tool.category)
    tool.monthly_cost = float(data.get('monthly_cost', tool.monthly_cost))
    tool.usage_level = data.get('usage_level', tool.usage_level)
    tool.efficiency_rating = float(data.get('efficiency_rating', tool.efficiency_rating))
    tool.user_count = int(data.get('user_count', tool.user_count))
    tool.status = data.get('status', tool.status)
    tool.updated_at = datetime.utcnow()

    db.session.commit()
    return jsonify(tool.to_dict())

@advanced_api.route('/api/ai-tools/<int:tool_id>', methods=['DELETE'])
@login_required
def delete_ai_tool(tool_id):
    """Delete an AI tool"""
    tool = AITool.query.filter_by(id=tool_id, user_id=current_user.id).first()
    if not tool:
        return jsonify({'error': 'Tool not found'}), 404

    db.session.delete(tool)
    db.session.commit()
    return jsonify({'message': 'Tool deleted successfully'})

@advanced_api.route('/api/ai-tools/scraper-data', methods=['GET'])
@login_required
def get_scraper_data():
    """Get AI tool pricing data from scraper"""
    try:
        # Load scraped pricing data
        try:
            with open('genai_tool_pricing.yaml', 'r') as f:
                scraper_data = yaml.safe_load(f)
        except FileNotFoundError:
            scraper_data = {}

        # Transform scraper data into a more usable format
        tools_catalog = []
        for tool_key, tool_data in scraper_data.items():
            if isinstance(tool_data, dict) and 'name' in tool_data:
                tools_catalog.append({
                    'key': tool_key,
                    'name': tool_data.get('name', tool_key),
                    'monthly_price': tool_data.get('price_per_license_usd_monthly', 0),
                    'features': tool_data.get('features', []),
                    'description': tool_data.get('description', ''),
                    'provider': tool_data.get('provider', 'Unknown'),
                    'category': tool_data.get('category', 'General AI'),
                    'last_updated': tool_data.get('last_updated', 'Unknown')
                })

        return jsonify({
            'tools_catalog': tools_catalog,
            'last_updated': 'Recent',
            'total_tools': len(tools_catalog)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/ai-tools/stats', methods=['GET'])
@login_required
def get_ai_tools_stats():
    """Get AI tools statistics for the current user"""
    try:
        tools = AITool.query.filter_by(user_id=current_user.id).all()

        # Calculate statistics
        total_tools = len(tools)
        active_tools = len([t for t in tools if t.status == 'active'])

        # Calculate actual monthly spend based on active users
        total_monthly_cost = 0

        for tool in tools:
            if tool.status == 'active':
                # Calculate actual spend: per-license cost × number of active users
                actual_tool_cost = tool.monthly_cost * tool.user_count if tool.user_count > 0 else tool.monthly_cost
                total_monthly_cost += actual_tool_cost

        total_users = sum(t.user_count for t in tools if t.status == 'active')

        # Calculate adoption rate (users with tools / total employees)
        employees = Employee.query.filter_by(user_id=current_user.id).all()
        total_employees = len(employees)
        adoption_rate = (total_users / max(1, total_employees)) * 100 if total_employees > 0 else 0

        return jsonify({
            'total_tools': total_tools,
            'active_tools': active_tools,
            'monthly_spend': total_monthly_cost,
            'total_users': total_users,
            'adoption_rate': round(adoption_rate, 1),
            'annual_spend': total_monthly_cost * 12
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ROI Configuration API
@advanced_api.route('/api/roi-configuration', methods=['GET'])
@login_required
def get_roi_configuration():
    """Get ROI configuration"""
    try:
        with open('roi_config.yaml', 'r') as f:
            roi_config = yaml.safe_load(f)
        return jsonify(roi_config)
    except FileNotFoundError:
        # Return default configuration
        default_config = {
            'job_family_roi': {},
            'global_settings': {'productivity_gain_base': 15}
        }
        return jsonify(default_config)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/roi-configuration', methods=['POST'])
@login_required
def update_roi_configuration():
    """Update ROI configuration"""
    try:
        data = request.get_json()

        # Save updated configuration
        with open('roi_config.yaml', 'w') as f:
            yaml.dump(data, f, default_flow_style=False)

        return jsonify({'message': 'ROI configuration updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Enhanced Dashboard Data
@advanced_api.route('/api/dashboard/metrics', methods=['GET'])
@login_required
def get_dashboard_metrics():
    """Get comprehensive dashboard metrics"""
    # Get user's tools and employees
    tools = AITool.query.filter_by(user_id=current_user.id).all()
    employees = Employee.query.filter_by(user_id=current_user.id).all()

    # Calculate metrics
    total_employee_spend = sum(emp.total_compensation for emp in employees)
    total_ai_spend = sum(tool.monthly_cost * 12 for tool in tools)

    # Mock ROI calculations (you can enhance this with real logic)
    predicted_roi = 15.0
    realized_roi = 12.0

    return jsonify({
        'employee_spend': total_employee_spend,
        'ai_spend_ytd': total_ai_spend,
        'predicted_roi': predicted_roi,
        'realized_roi': realized_roi,
        'total_employees': len(employees),
        'active_tools': len([t for t in tools if t.status == 'active'])
    })

@advanced_api.route('/api/dashboard/usage-analytics', methods=['GET'])
@login_required
def get_usage_analytics():
    """Get usage analytics for dashboard charts"""
    employees = Employee.query.filter_by(user_id=current_user.id).all()
    tools = AITool.query.filter_by(user_id=current_user.id).all()

    # Group by job function (simplified)
    job_functions = {}
    for emp in employees:
        if emp.level not in job_functions:
            job_functions[emp.level] = 0
        job_functions[emp.level] += 1

    # Tool usage distribution
    tool_usage = {}
    for tool in tools:
        tool_usage[tool.name] = tool.user_count

    return jsonify({
        'by_job_function': job_functions,
        'by_level': {'Senior': 60, 'Junior': 40},  # Mock data
        'by_model': tool_usage
    })

@advanced_api.route('/api/dashboard/top-employee-groups', methods=['GET'])
@login_required
def get_top_employee_groups():
    """Get top performing employee groups"""
    # This would typically involve complex analytics
    # For now, return mock data that matches the UI structure
    return jsonify([
        {
            'jobFamily': 'Software Engineer',
            'level': 'L5-L7',
            'count': 245,
            'avgROI': '16%',
            'totalCost': '$8.2M'
        },
        {
            'jobFamily': 'Product Manager',
            'level': 'L6-L8',
            'count': 89,
            'avgROI': '14%',
            'totalCost': '$3.1M'
        },
        {
            'jobFamily': 'Data Scientist',
            'level': 'L4-L6',
            'count': 156,
            'avgROI': '19%',
            'totalCost': '$4.8M'
        },
        {
            'jobFamily': 'Designer',
            'level': 'L4-L6',
            'count': 78,
            'avgROI': '11%',
            'totalCost': '$2.3M'
        }
    ])

# Employee Management API
@advanced_api.route('/api/employee-management', methods=['GET'])
@login_required
def get_employee_management_data():
    """Get comprehensive employee management data with ROI calculations"""
    try:
        # Get all employees for the current user
        employees = Employee.query.filter_by(user_id=current_user.id).all()

        if not employees:
            return jsonify({
                'employee_stats': {
                    'total_employees': 0,
                    'average_salary': 0,
                    'ai_adoption_rate': 0,
                    'productivity_gain': 0
                },
                'job_families': []
            })

        # Load ROI configuration
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Calculate employee statistics with weighted average
        total_employees = len(employees)

        # Define level weights (L3=1, L4=2, ..., L10=8)
        level_weights = {
            'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3,
            'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8
        }

        # Calculate weighted average salary
        total_weighted_compensation = 0
        total_weights = 0

        for emp in employees:
            weight = level_weights.get(emp.level, 1)  # Default weight 1 if level not found
            total_weighted_compensation += emp.total_compensation * weight
            total_weights += weight

        weighted_average_salary = total_weighted_compensation / total_weights if total_weights > 0 else 0

        # Group employees by job family
        job_family_data = defaultdict(list)
        for emp in employees:
            job_family_data[emp.job_family].append(emp)

        # Calculate job family performance
        job_families = []
        total_ai_adoption = 0
        total_roi_impact = 0

        for job_family, emp_list in job_family_data.items():
            count = len(emp_list)

            # Calculate weighted average salary for this job family
            family_weighted_compensation = 0
            family_total_weights = 0

            for emp in emp_list:
                weight = level_weights.get(emp.level, 1)
                family_weighted_compensation += emp.total_compensation * weight
                family_total_weights += weight

            avg_salary = family_weighted_compensation / family_total_weights if family_total_weights > 0 else 0

            # Get ROI config for this job family
            family_config = roi_config.get('job_family_roi', {}).get(job_family, {
                'base_ai_adoption': 70,
                'base_roi': 12,
                'level_multipliers': {}
            })

            # Calculate AI adoption and real ROI
            ai_adoption = family_config.get('base_ai_adoption', 70)

            # Calculate ROI and dollar savings using the updated function
            roi_data = calculate_job_family_roi(job_family, ai_adoption, current_user.id, roi_config)

            # Handle both old string format and new dict format for backward compatibility
            if isinstance(roi_data, dict):
                roi_display = roi_data['roi_percentage']
                savings_display = roi_data['annual_savings']
                roi_percentage = float(roi_data['roi_percentage'].replace('%', '').replace('+', ''))
            else:
                roi_display = roi_data
                savings_display = "N/A"
                roi_percentage = float(roi_data.replace('%', '').replace('+', '')) if roi_data != "0%" else 0

            total_ai_adoption += ai_adoption * count
            total_roi_impact += roi_percentage * count

            job_families.append({
                'name': job_family,
                'count': count,
                'avgSalary': f"${avg_salary/1000:.0f}K",
                'aiAdoption': f"{ai_adoption}%",
                'roi': roi_display,
                'savings': savings_display
            })

        # Calculate overall statistics
        overall_ai_adoption = total_ai_adoption / total_employees if total_employees > 0 else 0
        overall_roi = total_roi_impact / total_employees if total_employees > 0 else 0
        productivity_gain = roi_config.get('global_settings', {}).get('productivity_gain_base', 15)

        employee_stats = {
            'total_employees': total_employees,
            'average_salary': f"${weighted_average_salary/1000:.0f}K",
            'ai_adoption_rate': f"{overall_ai_adoption:.0f}%",
            'productivity_gain': f"+{productivity_gain}%"
        }

        return jsonify({
            'employee_stats': employee_stats,
            'job_families': job_families
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-management/adoption-rates', methods=['POST'])
@login_required
def update_adoption_rates():
    """Update AI adoption rates for job families"""
    try:
        data = request.get_json()
        adoption_rates = data.get('adoption_rates', {})

        # Load current ROI configuration
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Update adoption rates in the config
        for job_family, rate in adoption_rates.items():
            if job_family not in roi_config['job_family_roi']:
                roi_config['job_family_roi'][job_family] = {
                    'base_ai_adoption': rate,
                    'base_roi': 12,
                    'level_multipliers': {}
                }
            else:
                roi_config['job_family_roi'][job_family]['base_ai_adoption'] = rate

        # Save updated configuration
        with open('roi_config.yaml', 'w') as f:
            yaml.dump(roi_config, f, default_flow_style=False)

        return jsonify({'message': 'Adoption rates updated successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-management/update-adoption', methods=['POST'])
@login_required
def update_adoption_and_recalculate_roi():
    """Update adoption rate and recalculate ROI for a specific job family"""
    try:
        data = request.get_json()
        job_family = data.get('job_family')
        adoption_rate = data.get('adoption_rate')

        if not job_family or adoption_rate is None:
            return jsonify({'error': 'Missing job_family or adoption_rate'}), 400

        # Load ROI configuration
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Update adoption rate in config
        if job_family not in roi_config['job_family_roi']:
            roi_config['job_family_roi'][job_family] = {
                'base_ai_adoption': adoption_rate,
                'base_roi': 12,
                'level_multipliers': {}
            }
        else:
            roi_config['job_family_roi'][job_family]['base_ai_adoption'] = adoption_rate

        # Save updated configuration
        with open('roi_config.yaml', 'w') as f:
            yaml.dump(roi_config, f, default_flow_style=False)

        # Recalculate ROI for this job family
        roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

        # Handle both old string format and new dict format
        if isinstance(roi_data, dict):
            new_roi = roi_data['roi_percentage']
            new_savings = roi_data['annual_savings']
        else:
            new_roi = roi_data
            new_savings = "N/A"

        return jsonify({
            'message': 'Adoption rate updated successfully',
            'job_family': job_family,
            'new_adoption_rate': adoption_rate,
            'new_roi': new_roi,
            'new_savings': new_savings
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def calculate_job_family_roi(job_family, adoption_rate, user_id, roi_config):
    """Calculate ROI for a specific job family"""
    try:
        # Get employees for this job family
        employees = Employee.query.filter_by(user_id=user_id, job_family=job_family).all()

        if not employees:
            return "0%"

        count = len(employees)

        # Level weights for salary calculation
        level_weights = {
            'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3,
            'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8
        }

        # Calculate weighted average salary
        family_weighted_compensation = 0
        family_total_weights = 0

        for emp in employees:
            weight = level_weights.get(emp.level, 1)
            family_weighted_compensation += emp.total_compensation * weight
            family_total_weights += weight

        avg_salary = family_weighted_compensation / family_total_weights if family_total_weights > 0 else 0

        # Get costs and settings
        ai_tool_costs = roi_config.get('ai_tool_costs', {})
        monthly_cost_per_user = ai_tool_costs.get(job_family, 50)

        global_settings = roi_config.get('global_settings', {})
        productivity_gain = global_settings.get('productivity_gain_base', 15) / 100
        working_days = global_settings.get('working_days_per_month', 22)
        hours_per_day = global_settings.get('hours_per_day', 8)

        # Calculate ROI
        adoption_decimal = adoption_rate / 100
        monthly_ai_cost = count * monthly_cost_per_user

        hourly_salary = avg_salary / (12 * working_days * hours_per_day)
        adopted_employees = count * adoption_decimal
        monthly_productivity_value = adopted_employees * productivity_gain * hourly_salary * working_days * hours_per_day

        # Calculate ROI percentage and dollar savings
        monthly_savings = monthly_productivity_value - monthly_ai_cost
        annual_savings = monthly_savings * 12

        if monthly_ai_cost > 0:
            roi_percentage = (monthly_savings / monthly_ai_cost) * 100
        else:
            roi_percentage = 0

        if adoption_decimal == 0:
            roi_percentage = -100
            monthly_savings = -monthly_ai_cost  # Pure cost, no benefit
            annual_savings = monthly_savings * 12

        # Return both ROI% and dollar savings
        roi_display = f"{roi_percentage:+.0f}%" if roi_percentage != 0 else "0%"

        # Format dollar savings
        if abs(annual_savings) >= 1000000:
            savings_display = f"${annual_savings/1000000:+.1f}M/yr"
        elif abs(annual_savings) >= 1000:
            savings_display = f"${annual_savings/1000:+.0f}K/yr"
        else:
            savings_display = f"${annual_savings:+.0f}/yr"

        return {
            'roi_percentage': roi_display,
            'annual_savings': savings_display,
            'monthly_savings': monthly_savings,
            'annual_savings_raw': annual_savings
        }

    except Exception as e:
        print(f"Error calculating ROI: {e}")
        return "0%"

@advanced_api.route('/api/employee-management/update-employee', methods=['POST'])
@login_required
def update_employee_and_recalculate_roi():
    """Update employee data and recalculate ROI for affected job family"""
    try:
        data = request.get_json()
        employee_id = data.get('employee_id')
        updates = data.get('updates', {})

        if not employee_id:
            return jsonify({'error': 'Missing employee_id'}), 400

        # Get the employee
        employee = Employee.query.filter_by(id=employee_id, user_id=current_user.id).first()
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        old_job_family = employee.job_family

        # Update employee fields
        if 'total_compensation' in updates:
            employee.total_compensation = updates['total_compensation']
        if 'level' in updates:
            employee.level = updates['level']
        if 'job_family' in updates:
            employee.job_family = updates['job_family']
        if 'tenure_in_role' in updates:
            employee.tenure_in_role = updates['tenure_in_role']
        if 'tenure_since_last_hire' in updates:
            employee.tenure_since_last_hire = updates['tenure_since_last_hire']

        db.session.commit()

        # Load ROI configuration
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Recalculate ROI for affected job families
        affected_families = [old_job_family]
        if 'job_family' in updates and updates['job_family'] != old_job_family:
            affected_families.append(updates['job_family'])

        updated_rois = {}
        for job_family in set(affected_families):
            family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
            adoption_rate = family_config.get('base_ai_adoption', 70)
            roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

            # Handle both old string format and new dict format
            if isinstance(roi_data, dict):
                updated_rois[job_family] = {
                    'roi': roi_data['roi_percentage'],
                    'savings': roi_data['annual_savings']
                }
            else:
                updated_rois[job_family] = {'roi': roi_data, 'savings': 'N/A'}

        return jsonify({
            'message': 'Employee updated successfully',
            'employee_id': employee_id,
            'updated_rois': updated_rois,
            'affected_families': list(set(affected_families))
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-management/recalculate-all-roi', methods=['POST'])
@login_required
def recalculate_all_roi():
    """Recalculate ROI for all job families (useful after bulk updates)"""
    try:
        # Load ROI configuration
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Get all job families for current user
        employees = Employee.query.filter_by(user_id=current_user.id).all()
        job_families = list(set(emp.job_family for emp in employees))

        updated_rois = {}
        for job_family in job_families:
            family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
            adoption_rate = family_config.get('base_ai_adoption', 70)
            roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

            # Handle both old string format and new dict format
            if isinstance(roi_data, dict):
                updated_rois[job_family] = {
                    'roi': roi_data['roi_percentage'],
                    'savings': roi_data['annual_savings']
                }
            else:
                updated_rois[job_family] = {'roi': roi_data, 'savings': 'N/A'}

        return jsonify({
            'message': 'All ROI calculations updated successfully',
            'updated_rois': updated_rois
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-groups/add', methods=['POST'])
@login_required
def add_employee_groups():
    """Add new employee groups and create individual employee records"""
    try:
        data = request.get_json()
        groups = data.get('groups', [])

        if not groups:
            return jsonify({'error': 'No groups provided'}), 400

        created_employees = []
        created_groups = []

        for group_data in groups:
            job_family = group_data.get('jobFamilyName')
            employee_count = group_data.get('employeeCount', 0)
            avg_salary = group_data.get('avgSalary', 0)
            level = group_data.get('level', 'L5')

            if not job_family or employee_count <= 0 or avg_salary <= 0:
                continue

            # Create individual employee records for this group
            group_employees = []
            for i in range(employee_count):
                # Generate unique employee ID
                employee_id_str = f"{job_family[:3].upper()}{1000 + len(created_employees) + i + 1}"

                # Add some salary variance (±10%) to make it realistic
                import random
                salary_variance = random.uniform(0.9, 1.1)
                individual_salary = int(avg_salary * salary_variance)

                # Add some tenure variance
                tenure_in_role = random.randint(1, 5)
                tenure_since_hire = random.randint(1, 8)

                employee = Employee(
                    employee_id_str=employee_id_str,
                    job_family=job_family,
                    level=level,
                    total_compensation=individual_salary,
                    tenure_in_role=tenure_in_role,
                    tenure_since_last_hire=tenure_since_hire,
                    user_id=current_user.id
                )

                # Add data source tracking (could be added to Employee model later)
                # For now, we'll track this in the response

                db.session.add(employee)
                group_employees.append({
                    'employee_id': employee_id_str,
                    'salary': individual_salary,
                    'level': level
                })

            created_employees.extend(group_employees)
            created_groups.append({
                'job_family': job_family,
                'employee_count': employee_count,
                'avg_salary': avg_salary,
                'level': level,
                'employees': group_employees
            })

        # Commit all employee records
        db.session.commit()

        # Update ROI configuration for new job families
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'ai_tool_costs': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Add default ROI settings for new job families
        for group in created_groups:
            job_family = group['job_family']
            if job_family not in roi_config.get('job_family_roi', {}):
                # Set default adoption rate based on job family type
                default_adoption = get_default_adoption_rate(job_family)
                roi_config['job_family_roi'][job_family] = {
                    'base_ai_adoption': default_adoption,
                    'base_roi': 12,
                    'level_multipliers': {}
                }

            # Set default AI tool cost if not exists
            if job_family not in roi_config.get('ai_tool_costs', {}):
                default_cost = get_default_tool_cost(job_family)
                roi_config['ai_tool_costs'][job_family] = default_cost

        # Save updated ROI configuration
        with open('roi_config.yaml', 'w') as f:
            yaml.dump(roi_config, f, default_flow_style=False)

        # Calculate ROI for all affected job families
        updated_rois = {}
        for group in created_groups:
            job_family = group['job_family']
            family_config = roi_config['job_family_roi'][job_family]
            adoption_rate = family_config['base_ai_adoption']
            roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

            if isinstance(roi_data, dict):
                updated_rois[job_family] = {
                    'roi': roi_data['roi_percentage'],
                    'savings': roi_data['annual_savings']
                }
            else:
                updated_rois[job_family] = {'roi': roi_data, 'savings': 'N/A'}

        return jsonify({
            'message': f'Successfully added {len(created_employees)} employees across {len(created_groups)} groups',
            'created_groups': created_groups,
            'total_employees_added': len(created_employees),
            'updated_rois': updated_rois
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def get_default_adoption_rate(job_family):
    """Get default AI adoption rate based on job family"""
    adoption_rates = {
        'Software Engineer': 85,
        'Frontend Engineer': 80,
        'Backend Engineer': 85,
        'Full Stack Engineer': 85,
        'Data Scientist': 95,
        'Senior Data Scientist': 95,
        'Product Manager': 75,
        'Senior Product Manager': 80,
        'Designer': 70,
        'UX Designer': 75,
        'UI Designer': 70,
        'Research Scientist': 90,
        'DevOps Engineer': 80,
        'QA Engineer': 70,
        'Marketing Manager': 60,
        'Sales Representative': 50
    }
    return adoption_rates.get(job_family, 70)  # Default 70%

def get_default_tool_cost(job_family):
    """Get default AI tool cost based on job family"""
    tool_costs = {
        'Software Engineer': 50,
        'Frontend Engineer': 45,
        'Backend Engineer': 50,
        'Full Stack Engineer': 55,
        'Data Scientist': 75,
        'Senior Data Scientist': 85,
        'Product Manager': 30,
        'Senior Product Manager': 40,
        'Designer': 35,
        'UX Designer': 40,
        'UI Designer': 30,
        'Research Scientist': 80,
        'DevOps Engineer': 45,
        'QA Engineer': 35,
        'Marketing Manager': 25,
        'Sales Representative': 20
    }
    return tool_costs.get(job_family, 50)  # Default $50/month

@advanced_api.route('/api/employee-groups/update-salary', methods=['POST'])
@login_required
def update_job_family_salary():
    """Update average salary for a job family and recalculate ROI"""
    try:
        data = request.get_json()
        job_family = data.get('job_family')
        new_avg_salary = data.get('new_avg_salary', 0)

        if not job_family or new_avg_salary <= 0:
            return jsonify({'error': 'Invalid job family or salary'}), 400

        # Get all employees in this job family
        employees = Employee.query.filter_by(
            user_id=current_user.id,
            job_family=job_family
        ).all()

        if not employees:
            return jsonify({'error': f'No employees found in {job_family}'}), 404

        # Calculate current weighted average for comparison
        total_comp = 0
        total_weight = 0
        level_weights = {'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3, 'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8}

        for emp in employees:
            weight = level_weights.get(emp.level, 1)
            total_comp += emp.total_compensation * weight
            total_weight += weight

        current_weighted_avg = total_comp / total_weight if total_weight > 0 else 0

        # Calculate adjustment factor
        adjustment_factor = new_avg_salary / current_weighted_avg if current_weighted_avg > 0 else 1

        # Update all employee salaries proportionally
        updated_employees = []
        for emp in employees:
            old_salary = emp.total_compensation
            new_salary = int(old_salary * adjustment_factor)
            emp.total_compensation = new_salary

            updated_employees.append({
                'employee_id': emp.employee_id_str,
                'old_salary': old_salary,
                'new_salary': new_salary,
                'level': emp.level
            })

        # Commit salary changes
        db.session.commit()

        # Recalculate ROI for this job family
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'ai_tool_costs': {}, 'global_settings': {'productivity_gain_base': 15}}

        family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
        adoption_rate = family_config.get('base_ai_adoption', 70)
        roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

        # Format ROI data
        if isinstance(roi_data, dict):
            new_roi = roi_data['roi_percentage']
            new_savings = roi_data['annual_savings']
        else:
            new_roi = roi_data
            new_savings = 'N/A'

        # Verify new weighted average
        total_comp_new = 0
        total_weight_new = 0
        for emp in employees:
            weight = level_weights.get(emp.level, 1)
            total_comp_new += emp.total_compensation * weight
            total_weight_new += weight

        actual_new_avg = total_comp_new / total_weight_new if total_weight_new > 0 else 0

        return jsonify({
            'message': f'Successfully updated salaries for {len(employees)} employees in {job_family}',
            'job_family': job_family,
            'old_avg_salary': current_weighted_avg,
            'new_avg_salary': actual_new_avg,
            'target_avg_salary': new_avg_salary,
            'adjustment_factor': adjustment_factor,
            'updated_employees': updated_employees,
            'new_roi': new_roi,
            'new_savings': new_savings,
            'employee_count': len(employees)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-groups/update-count', methods=['POST'])
@login_required
def update_job_family_count():
    """Update employee count for a job family and recalculate ROI"""
    try:
        data = request.get_json()
        job_family = data.get('job_family')
        new_count = data.get('new_count', 0)

        if not job_family or new_count < 0:
            return jsonify({'error': 'Invalid job family or count'}), 400

        # Get current employees in this job family
        current_employees = Employee.query.filter_by(
            user_id=current_user.id,
            job_family=job_family
        ).all()

        current_count = len(current_employees)

        if new_count == current_count:
            return jsonify({'message': 'No change needed', 'current_count': current_count}), 200

        # Calculate weighted average salary for new employees
        if current_employees:
            total_comp = 0
            total_weight = 0
            level_weights = {'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3, 'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8}

            for emp in current_employees:
                weight = level_weights.get(emp.level, 1)
                total_comp += emp.total_compensation * weight
                total_weight += weight

            avg_salary = total_comp / total_weight if total_weight > 0 else 150000

            # Get level distribution for new employees
            level_distribution = {}
            for emp in current_employees:
                level_distribution[emp.level] = level_distribution.get(emp.level, 0) + 1
        else:
            avg_salary = 150000  # Default salary for new job families
            level_distribution = {'L5': 1}  # Default to L5

        changes_made = []

        if new_count > current_count:
            # Add new employees
            employees_to_add = new_count - current_count

            # Determine levels for new employees based on existing distribution
            levels_to_add = []
            if level_distribution:
                # Replicate existing distribution
                total_existing = sum(level_distribution.values())
                for level, count in level_distribution.items():
                    proportion = count / total_existing
                    new_level_count = max(1, round(employees_to_add * proportion))
                    levels_to_add.extend([level] * new_level_count)

                # Adjust if we have too many or too few
                while len(levels_to_add) < employees_to_add:
                    levels_to_add.append('L5')  # Default level
                levels_to_add = levels_to_add[:employees_to_add]
            else:
                levels_to_add = ['L5'] * employees_to_add

            # Create new employees
            for i, level in enumerate(levels_to_add):
                # Generate unique employee ID
                employee_id_str = f"{job_family[:3].upper()}{2000 + current_count + i + 1}"

                # Add salary variance (±10%)
                import random
                salary_variance = random.uniform(0.9, 1.1)
                individual_salary = int(avg_salary * salary_variance)

                # Add tenure variance
                tenure_in_role = random.randint(1, 5)
                tenure_since_hire = random.randint(1, 8)

                employee = Employee(
                    employee_id_str=employee_id_str,
                    job_family=job_family,
                    level=level,
                    total_compensation=individual_salary,
                    tenure_in_role=tenure_in_role,
                    tenure_since_last_hire=tenure_since_hire,
                    user_id=current_user.id
                )

                db.session.add(employee)
                changes_made.append({
                    'action': 'added',
                    'employee_id': employee_id_str,
                    'level': level,
                    'salary': individual_salary
                })

        elif new_count < current_count:
            # Remove employees (remove from end of list to maintain consistency)
            employees_to_remove = current_count - new_count
            employees_to_delete = current_employees[-employees_to_remove:]

            for emp in employees_to_delete:
                changes_made.append({
                    'action': 'removed',
                    'employee_id': emp.employee_id_str,
                    'level': emp.level,
                    'salary': emp.total_compensation
                })
                db.session.delete(emp)

        # Commit changes
        db.session.commit()

        # Recalculate ROI for this job family
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'ai_tool_costs': {}, 'global_settings': {'productivity_gain_base': 15}}

        family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
        adoption_rate = family_config.get('base_ai_adoption', 70)
        roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

        # Format ROI data
        if isinstance(roi_data, dict):
            new_roi = roi_data['roi_percentage']
            new_savings = roi_data['annual_savings']
        else:
            new_roi = roi_data
            new_savings = 'N/A'

        # Calculate new weighted average salary
        updated_employees = Employee.query.filter_by(
            user_id=current_user.id,
            job_family=job_family
        ).all()

        if updated_employees:
            total_comp = 0
            total_weight = 0
            level_weights = {'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3, 'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8}

            for emp in updated_employees:
                weight = level_weights.get(emp.level, 1)
                total_comp += emp.total_compensation * weight
                total_weight += weight

            new_avg_salary = total_comp / total_weight if total_weight > 0 else 0
        else:
            new_avg_salary = 0

        return jsonify({
            'message': f'Successfully updated {job_family} from {current_count} to {new_count} employees',
            'job_family': job_family,
            'old_count': current_count,
            'new_count': new_count,
            'changes_made': changes_made,
            'new_avg_salary': new_avg_salary,
            'new_roi': new_roi,
            'new_savings': new_savings,
            'employees_added': len([c for c in changes_made if c['action'] == 'added']),
            'employees_removed': len([c for c in changes_made if c['action'] == 'removed'])
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-groups/split', methods=['POST'])
@login_required
def split_employee_group():
    """Split an existing employee group into a new job family"""
    try:
        data = request.get_json()
        source_job_family = data.get('sourceJobFamily')
        new_job_family = data.get('newJobFamily')
        split_count = data.get('splitCount', 0)

        if not source_job_family or not new_job_family or split_count <= 0:
            return jsonify({'error': 'Missing required fields'}), 400

        # Get employees from source job family
        source_employees = Employee.query.filter_by(
            user_id=current_user.id,
            job_family=source_job_family
        ).limit(split_count).all()

        if len(source_employees) < split_count:
            return jsonify({'error': f'Not enough employees in {source_job_family}. Available: {len(source_employees)}'}), 400

        # Update the selected employees to new job family
        moved_employees = []
        for employee in source_employees:
            employee.job_family = new_job_family
            moved_employees.append({
                'employee_id': employee.employee_id_str,
                'salary': employee.total_compensation,
                'level': employee.level
            })

        db.session.commit()

        # Update ROI configuration for new job family
        try:
            with open('roi_config.yaml', 'r') as f:
                roi_config = yaml.safe_load(f)
        except FileNotFoundError:
            roi_config = {'job_family_roi': {}, 'ai_tool_costs': {}, 'global_settings': {'productivity_gain_base': 15}}

        # Add default ROI settings for new job family if not exists
        if new_job_family not in roi_config.get('job_family_roi', {}):
            # Inherit adoption rate from source job family or use default
            source_config = roi_config.get('job_family_roi', {}).get(source_job_family, {})
            default_adoption = source_config.get('base_ai_adoption', get_default_adoption_rate(new_job_family))

            roi_config['job_family_roi'][new_job_family] = {
                'base_ai_adoption': default_adoption,
                'base_roi': 12,
                'level_multipliers': source_config.get('level_multipliers', {})
            }

        # Set default AI tool cost if not exists
        if new_job_family not in roi_config.get('ai_tool_costs', {}):
            # Inherit cost from source or use default
            source_cost = roi_config.get('ai_tool_costs', {}).get(source_job_family, 50)
            roi_config['ai_tool_costs'][new_job_family] = source_cost

        # Save updated ROI configuration
        with open('roi_config.yaml', 'w') as f:
            yaml.dump(roi_config, f, default_flow_style=False)

        # Calculate ROI for both affected job families
        updated_rois = {}
        for job_family in [source_job_family, new_job_family]:
            family_config = roi_config['job_family_roi'][job_family]
            adoption_rate = family_config['base_ai_adoption']
            roi_data = calculate_job_family_roi(job_family, adoption_rate, current_user.id, roi_config)

            if isinstance(roi_data, dict):
                updated_rois[job_family] = {
                    'roi': roi_data['roi_percentage'],
                    'savings': roi_data['annual_savings']
                }
            else:
                updated_rois[job_family] = {'roi': roi_data, 'savings': 'N/A'}

        return jsonify({
            'message': f'Successfully moved {split_count} employees from {source_job_family} to {new_job_family}',
            'source_job_family': source_job_family,
            'new_job_family': new_job_family,
            'moved_employees': moved_employees,
            'split_count': split_count,
            'updated_rois': updated_rois
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@advanced_api.route('/api/employee-groups/existing', methods=['GET'])
@login_required
def get_existing_groups():
    """Get existing employee groups for splitting"""
    try:
        # Get job family counts
        from sqlalchemy import func
        job_family_counts = db.session.query(
            Employee.job_family,
            func.count(Employee.id).label('count'),
            func.avg(Employee.total_compensation).label('avg_salary')
        ).filter_by(user_id=current_user.id).group_by(Employee.job_family).all()

        existing_groups = []
        for job_family, count, avg_salary in job_family_counts:
            existing_groups.append({
                'id': job_family,  # Use job_family as ID for simplicity
                'name': job_family,
                'count': count,
                'avgSalary': f"${avg_salary/1000:.0f}K" if avg_salary else "$0K"
            })

        return jsonify({
            'existing_groups': existing_groups
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500