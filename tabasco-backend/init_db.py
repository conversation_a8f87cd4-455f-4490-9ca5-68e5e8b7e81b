#!/usr/bin/env python3
"""
Database initialization script for Tabasco AI Tools Management
Creates all necessary tables and ensures proper schema
"""

from app import create_app
from extensions import db
from models import User, Employee, AIModel, AITool
import os

def init_database():
    """Initialize the database with all tables"""
    app = create_app()
    
    with app.app_context():
        try:
            # Create all tables
            print("Creating database tables...")
            db.create_all()
            
            # Verify tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"✅ Database initialized successfully!")
            print(f"📊 Created tables: {', '.join(tables)}")
            
            # Check if AITool table has correct columns
            if 'ai_tool' in tables:
                columns = [col['name'] for col in inspector.get_columns('ai_tool')]
                print(f"🔧 AITool table columns: {', '.join(columns)}")
                
                required_columns = ['id', 'name', 'provider', 'category', 'monthly_cost', 
                                  'usage_level', 'efficiency_rating', 'user_count', 'status', 
                                  'created_at', 'updated_at', 'user_id']
                
                missing_columns = [col for col in required_columns if col not in columns]
                if missing_columns:
                    print(f"⚠️  Missing columns: {', '.join(missing_columns)}")
                    print("💡 You may need to run database migrations")
                else:
                    print("✅ All required columns present")
            
            return True
            
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return False

if __name__ == "__main__":
    print("🚀 Initializing Tabasco Database...")
    success = init_database()
    
    if success:
        print("\n🎉 Database initialization complete!")
        print("💡 You can now start the application")
    else:
        print("\n💥 Database initialization failed!")
        print("🔧 Please check your database configuration")
