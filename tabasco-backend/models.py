from flask_login import UserMixin
from extensions import db # Import from extensions
from datetime import datetime

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(200), nullable=False) # Increased length for hash
    employees = db.relationship('Employee', backref='owner', lazy='dynamic')

    def __repr__(self):
        return f'<User {self.username}>'

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id_str = db.Column(db.String(50), nullable=False, unique=False) # Changed name to avoid conflict, not unique across all users
    job_family = db.Column(db.String(100), nullable=False) # e.g., Software Engineer, Product Manager
    tenure_in_role = db.Column(db.Integer, nullable=False) # in years
    level = db.Column(db.String(10), nullable=False) # e.g., L1, L2, ... L10
    tenure_since_last_hire = db.Column(db.Integer, nullable=False) # in years
    total_compensation = db.Column(db.Integer, nullable=False) # Annual compensation in USD
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    def __repr__(self):
        return f'<Employee {self.employee_id_str} - Level {self.level}>'

class AIModel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    provider = db.Column(db.String(50), nullable=False)
    development_cost = db.Column(db.Float, nullable=False, default=0)
    implementation_cost = db.Column(db.Float, nullable=False, default=0)
    maintenance_cost = db.Column(db.Float, nullable=False, default=0)
    infrastructure_cost = db.Column(db.Float, nullable=False, default=0)
    status = db.Column(db.String(20), nullable=False, default='planned')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'provider': self.provider,
            'development': self.development_cost,
            'implementation': self.implementation_cost,
            'maintenance': self.maintenance_cost,
            'infrastructure': self.infrastructure_cost,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class AITool(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    provider = db.Column(db.String(50), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    monthly_cost = db.Column(db.Float, nullable=False)
    usage_level = db.Column(db.String(20), nullable=False, default='medium')
    efficiency_rating = db.Column(db.Float, default=0)
    user_count = db.Column(db.Integer, default=0)
    status = db.Column(db.String(20), nullable=False, default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'provider': self.provider,
            'category': self.category,
            'monthly_cost': self.monthly_cost,
            'usage_level': self.usage_level,
            'efficiency_rating': self.efficiency_rating,
            'user_count': self.user_count,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

# To ensure the app context is available for db operations if this file is run directly (not typical)
# from app import create_app
# app = create_app()
# with app.app_context():
#     db.create_all()
