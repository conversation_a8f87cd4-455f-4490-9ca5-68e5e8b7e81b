#!/usr/bin/env python3
"""
Test script to verify login functionality
"""

import requests
import json

def test_login():
    """Test the login API endpoint"""
    base_url = 'http://localhost:5004'
    
    # Test login
    login_data = {
        'username': 'demo',
        'password': 'demo123'
    }
    
    try:
        print("🔍 Testing login API...")
        response = requests.post(
            f'{base_url}/api/login',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful!")
            print(f"📊 Response Data: {json.dumps(data, indent=2)}")
            
            # Test user endpoint with session
            print("\n🔍 Testing user endpoint...")
            user_response = requests.get(
                f'{base_url}/api/user',
                cookies=response.cookies
            )
            print(f"📊 User endpoint status: {user_response.status_code}")
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"📊 User data: {json.dumps(user_data, indent=2)}")
            else:
                print(f"❌ User endpoint failed: {user_response.text}")
                
        else:
            print(f"❌ Login failed: {response.text}")
            
    except Exception as e:
        print(f"💥 Error testing login: {e}")

if __name__ == "__main__":
    test_login()
