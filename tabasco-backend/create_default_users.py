#!/usr/bin/env python3
"""
Enhanced script to create default users for Tabasco application
Creates both demo and admin accounts automatically
"""

from app import create_app
from extensions import db
from models import User
from werkzeug.security import generate_password_hash

# Default users configuration
DEFAULT_USERS = [
    {
        'username': 'demo',
        'email': '<EMAIL>',
        'password': 'demo123',
        'description': 'Demo user for testing and demonstrations'
    },
    {
        'username': 'admin',
        'email': '<EMAIL>',
        'password': 'password',
        'description': 'Administrator user with full access'
    }
]

def create_user(username, email, password, description):
    """Create a single user with proper error handling"""
    try:
        # Check if user already exists
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            print(f"👤 User '{username}' already exists, updating password...")
            existing_user.password_hash = generate_password_hash(password, method='pbkdf2:sha256')
            existing_user.email = email
            db.session.commit()
            print(f"✅ Updated user '{username}' successfully!")
        else:
            # Create new user
            password_hash = generate_password_hash(password, method='pbkdf2:sha256')
            user = User(
                username=username,
                email=email,
                password_hash=password_hash
            )
            db.session.add(user)
            db.session.commit()
            print(f"✅ Created user '{username}' successfully!")

        print(f"📊 {description}")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        print(f"   Email: {email}")
        print("")

        return True

    except Exception as e:
        print(f"❌ Error creating user '{username}': {e}")
        db.session.rollback()
        return False

def create_default_users():
    """Create all default users for the application"""
    app = create_app()

    with app.app_context():
        print("🌶️  Setting up Tabasco default users...")
        print("=" * 50)

        success_count = 0
        total_users = len(DEFAULT_USERS)

        for user_config in DEFAULT_USERS:
            success = create_user(
                username=user_config['username'],
                email=user_config['email'],
                password=user_config['password'],
                description=user_config['description']
            )
            if success:
                success_count += 1

        print("=" * 50)
        if success_count == total_users:
            print(f"🎉 All {total_users} default users created successfully!")
            print("🚀 Tabasco is ready to use!")
        else:
            print(f"⚠️  Created {success_count}/{total_users} users")
            print("💥 Some users failed to create")

        print("\n📋 Quick Start:")
        print("   • Demo User: demo / demo123")
        print("   • Admin User: admin / password")
        print("   • Frontend: http://localhost:3000")
        print("   • Backend API: http://localhost:5004")

        return success_count == total_users

if __name__ == "__main__":
    success = create_default_users()
    if not success:
        exit(1)
