"""
Production Configuration for Tabasco Backend
Optimized for Digital Ocean App Platform deployment
"""

import os
from datetime import timedelta

class ProductionConfig:
    """Production configuration settings"""

    # Flask Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'fallback-secret-key-change-in-production'
    DEBUG = False
    TESTING = False

    # Database Configuration
    # Digital Ocean App Platform will provide DATABASE_URL for managed databases
    DATABASE_URL = os.environ.get('DATABASE_URL')
    if DATABASE_URL and DATABASE_URL.startswith('postgres://'):
        # Fix for SQLAlchemy 1.4+ which requires postgresql:// instead of postgres://
        DATABASE_URL = DATABASE_URL.replace('postgres://', 'postgresql://', 1)

    # Fallback to SQLite for development/testing
    SQLALCHEMY_DATABASE_URI = DATABASE_URL or 'sqlite:///tabasco.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }

    # CORS Configuration
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    CORS_ALLOW_CREDENTIALS = True

    # Server Configuration
    HOST = '0.0.0.0'  # Listen on all interfaces for Digital Ocean
    PORT = int(os.environ.get('PORT', 8080))  # Use Digital Ocean's PORT or default to 8080

    # Session Configuration for Cross-Origin (Vercel + Digital Ocean)
    SESSION_COOKIE_SECURE = True  # HTTPS only
    SESSION_COOKIE_HTTPONLY = False  # Allow JavaScript access for debugging
    SESSION_COOKIE_SAMESITE = 'None'  # Required for cross-origin cookies
    SESSION_COOKIE_DOMAIN = None  # Allow cross-domain cookies
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)

    # Additional session settings
    SESSION_PERMANENT = True
    SESSION_USE_SIGNER = True

    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
    }

    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s %(levelname)s %(name)s %(message)s'

    # Application Settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file upload

    # Rate Limiting (if implemented)
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL', 'memory://')

    # Health Check Settings
    HEALTH_CHECK_ENDPOINT = '/api/health'

    @staticmethod
    def init_app(app):
        """Initialize production-specific app configuration"""

        # Configure logging
        import logging
        from logging.handlers import RotatingFileHandler

        if not app.debug and not app.testing:
            # File logging
            if not os.path.exists('logs'):
                os.mkdir('logs')

            file_handler = RotatingFileHandler(
                'logs/tabasco.log',
                maxBytes=10240000,
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(ProductionConfig.LOG_FORMAT))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)

            # Console logging for Digital Ocean
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(ProductionConfig.LOG_FORMAT))
            console_handler.setLevel(logging.INFO)
            app.logger.addHandler(console_handler)

            app.logger.setLevel(logging.INFO)
            app.logger.info('Tabasco backend startup')

class DevelopmentConfig:
    """Development configuration for local testing"""

    SECRET_KEY = 'dev-secret-key'
    DEBUG = True
    TESTING = False
    SQLALCHEMY_DATABASE_URI = 'sqlite:///tabasco_dev.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002']
    CORS_ALLOW_CREDENTIALS = True
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
