#!/usr/bin/env python3
"""
Test script for Editable Employee Count functionality
"""

import requests
import json

BASE_URL = 'http://localhost:5004'

def test_count_increase():
    """Test increasing employee count"""
    print("🧪 Testing Employee Count Increase")
    print("=" * 50)
    
    test_cases = [
        {
            'job_family': 'Software Engineer',
            'new_count': 12,
            'description': 'Increase Software Engineers from 8 to 12'
        },
        {
            'job_family': 'Product Manager',
            'new_count': 8,
            'description': 'Increase Product Managers from 5 to 8'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 Test Case {i}: {test_case['description']}")
        print("-" * 40)
        
        payload = {
            'job_family': test_case['job_family'],
            'new_count': test_case['new_count']
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-count",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Successfully updated {test_case['job_family']} count!")
                print(f"📊 Old Count: {result['old_count']}")
                print(f"📊 New Count: {result['new_count']}")
                print(f"👥 Employees Added: {result['employees_added']}")
                print(f"👥 Employees Removed: {result['employees_removed']}")
                print(f"💰 New Avg Salary: ${result['new_avg_salary']:,.0f}")
                print(f"📈 New ROI: {result['new_roi']}")
                print(f"💵 New Savings: {result['new_savings']}")
                
                # Show changes made
                if result['changes_made']:
                    print(f"\n📋 Changes Made:")
                    for change in result['changes_made'][:5]:  # Show first 5
                        action = change['action']
                        emp_id = change['employee_id']
                        level = change['level']
                        salary = change['salary']
                        print(f"   • {action.title()}: {emp_id} ({level}) - ${salary:,}")
                    
                    if len(result['changes_made']) > 5:
                        print(f"   ... and {len(result['changes_made']) - 5} more changes")
                        
            else:
                print(f"❌ Failed to update count: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing count increase: {e}")

def test_count_decrease():
    """Test decreasing employee count"""
    print("\n\n🧪 Testing Employee Count Decrease")
    print("=" * 50)
    
    test_cases = [
        {
            'job_family': 'Software Engineer',
            'new_count': 6,
            'description': 'Decrease Software Engineers to 6 (layoffs scenario)'
        },
        {
            'job_family': 'Designer',
            'new_count': 2,
            'description': 'Decrease Designers to 2 (team restructure)'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 Test Case {i}: {test_case['description']}")
        print("-" * 40)
        
        payload = {
            'job_family': test_case['job_family'],
            'new_count': test_case['new_count']
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-count",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Successfully reduced {test_case['job_family']} count!")
                print(f"📊 Old Count: {result['old_count']}")
                print(f"📊 New Count: {result['new_count']}")
                print(f"👥 Employees Removed: {result['employees_removed']}")
                print(f"💰 New Avg Salary: ${result['new_avg_salary']:,.0f}")
                print(f"📈 New ROI: {result['new_roi']}")
                print(f"💵 New Savings: {result['new_savings']}")
                
                # Show removed employees
                removed_employees = [c for c in result['changes_made'] if c['action'] == 'removed']
                if removed_employees:
                    print(f"\n📋 Employees Removed:")
                    for emp in removed_employees:
                        print(f"   • {emp['employee_id']} ({emp['level']}) - ${emp['salary']:,}")
                        
            else:
                print(f"❌ Failed to update count: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing count decrease: {e}")

def test_invalid_count_updates():
    """Test invalid count update scenarios"""
    print("\n\n🧪 Testing Invalid Count Updates")
    print("=" * 50)
    
    invalid_cases = [
        {
            'job_family': 'Software Engineer',
            'new_count': -5,
            'description': 'Negative count (should fail)'
        },
        {
            'job_family': 'Nonexistent Role',
            'new_count': 10,
            'description': 'Nonexistent job family (should fail)'
        },
        {
            'job_family': '',
            'new_count': 5,
            'description': 'Empty job family (should fail)'
        }
    ]
    
    for i, test_case in enumerate(invalid_cases, 1):
        print(f"\n🔄 Invalid Test {i}: {test_case['description']}")
        print("-" * 40)
        
        payload = {
            'job_family': test_case['job_family'],
            'new_count': test_case['new_count']
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-count",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code != 200:
                print(f"✅ Correctly rejected invalid request: {response.status_code}")
                error_data = response.json()
                print(f"📝 Error message: {error_data.get('error', 'Unknown error')}")
            else:
                print(f"❌ Unexpectedly accepted invalid request")
                print(f"Response: {response.json()}")
                
        except Exception as e:
            print(f"❌ Error testing invalid count: {e}")

def test_roi_impact_of_scaling():
    """Test ROI impact of different team sizes"""
    print("\n\n🧪 Testing ROI Impact of Team Scaling")
    print("=" * 50)
    
    # Test different team sizes for Software Engineers
    team_sizes = [3, 6, 10, 15, 20]
    job_family = 'Software Engineer'
    
    print(f"📊 Testing ROI impact for {job_family} at different team sizes:")
    print("-" * 60)
    
    for size in team_sizes:
        payload = {
            'job_family': job_family,
            'new_count': size
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/employee-groups/update-count",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"👥 Team Size: {size:2d} → ROI: {result['new_roi']:>8s} → Savings: {result['new_savings']:>12s}")
            else:
                print(f"❌ Failed at size {size}: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error at size {size}: {e}")

def test_zero_employees():
    """Test setting employee count to zero"""
    print("\n\n🧪 Testing Zero Employee Count")
    print("=" * 50)
    
    payload = {
        'job_family': 'QA Engineer',
        'new_count': 0
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/employee-groups/update-count",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Successfully set QA Engineer count to 0!")
            print(f"📊 Old Count: {result['old_count']}")
            print(f"📊 New Count: {result['new_count']}")
            print(f"👥 Employees Removed: {result['employees_removed']}")
            print(f"💰 New Avg Salary: ${result['new_avg_salary']:,.0f}")
            print(f"📈 New ROI: {result['new_roi']}")
            print(f"💵 New Savings: {result['new_savings']}")
            print(f"💡 This simulates completely eliminating a job family")
        else:
            print(f"❌ Failed to set count to zero: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing zero count: {e}")

if __name__ == '__main__':
    print("🚀 Testing Editable Employee Count Functionality")
    print("=" * 60)
    
    # Run all tests
    test_count_increase()
    test_count_decrease()
    test_invalid_count_updates()
    test_roi_impact_of_scaling()
    test_zero_employees()
    
    print("\n✅ All employee count tests completed!")
