#!/usr/bin/env python3
"""
Test script for Employee Group Management functionality
"""

import requests
import json

BASE_URL = 'http://localhost:5004'

def test_add_employee_groups():
    """Test adding new employee groups"""
    print("🧪 Testing Add Employee Groups API")
    print("=" * 50)
    
    # Sample employee groups data
    test_groups = [
        {
            "jobFamilyName": "Frontend Engineer",
            "employeeCount": 8,
            "avgSalary": 145000,
            "level": "L4"
        },
        {
            "jobFamilyName": "Backend Engineer", 
            "employeeCount": 12,
            "avgSalary": 155000,
            "level": "L5"
        },
        {
            "jobFamilyName": "QA Engineer",
            "employeeCount": 5,
            "avgSalary": 125000,
            "level": "L4"
        }
    ]
    
    # Test data
    payload = {
        "groups": test_groups
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/employee-groups/add",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully added employee groups!")
            print(f"📊 Groups created: {len(result['created_groups'])}")
            print(f"👥 Total employees added: {result['total_employees_added']}")
            print(f"💰 Updated ROIs: {json.dumps(result['updated_rois'], indent=2)}")
            
            for group in result['created_groups']:
                print(f"\n📋 {group['job_family']}:")
                print(f"   • {group['employee_count']} employees")
                print(f"   • ${group['avg_salary']:,} average salary")
                print(f"   • {group['level']} level")
                print(f"   • {len(group['employees'])} individual records created")
        else:
            print(f"❌ Failed to add groups: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing add groups: {e}")

def test_get_existing_groups():
    """Test getting existing employee groups"""
    print("\n🧪 Testing Get Existing Groups API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/employee-groups/existing")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully fetched existing groups!")
            
            for group in result['existing_groups']:
                print(f"📋 {group['name']}: {group['count']} employees, {group['avgSalary']} avg salary")
        else:
            print(f"❌ Failed to get groups: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing get groups: {e}")

def test_split_employee_group():
    """Test splitting an existing employee group"""
    print("\n🧪 Testing Split Employee Group API")
    print("=" * 50)
    
    # First get existing groups to find one to split
    try:
        response = requests.get(f"{BASE_URL}/api/employee-groups/existing")
        if response.status_code == 200:
            existing_groups = response.json()['existing_groups']
            
            if existing_groups:
                # Find a group with enough employees to split
                source_group = None
                for group in existing_groups:
                    if group['count'] >= 3:  # Need at least 3 to split
                        source_group = group
                        break
                
                if source_group:
                    split_payload = {
                        "sourceJobFamily": source_group['name'],
                        "newJobFamily": f"Senior {source_group['name']}",
                        "splitCount": 2
                    }
                    
                    response = requests.post(
                        f"{BASE_URL}/api/employee-groups/split",
                        json=split_payload,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print("✅ Successfully split employee group!")
                        print(f"📊 Moved {result['split_count']} employees")
                        print(f"📋 From: {result['source_job_family']}")
                        print(f"📋 To: {result['new_job_family']}")
                        print(f"💰 Updated ROIs: {json.dumps(result['updated_rois'], indent=2)}")
                    else:
                        print(f"❌ Failed to split group: {response.status_code}")
                        print(f"Error: {response.text}")
                else:
                    print("⚠️ No groups with enough employees to split")
            else:
                print("⚠️ No existing groups found")
        else:
            print(f"❌ Failed to get existing groups: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing split group: {e}")

def test_employee_management_data():
    """Test the employee management data endpoint"""
    print("\n🧪 Testing Employee Management Data API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/employee-management")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully fetched employee management data!")
            print(f"📊 Total employees: {result['employee_stats']['total_employees']}")
            print(f"💰 Average salary: {result['employee_stats']['average_salary']}")
            print(f"🤖 AI adoption: {result['employee_stats']['ai_adoption_rate']}")
            print(f"📈 Productivity gain: {result['employee_stats']['productivity_gain']}")
            
            print(f"\n📋 Job Families ({len(result['job_families'])}):")
            for family in result['job_families']:
                print(f"   • {family['name']}: {family['count']} employees, {family['roi']} ROI")
                if 'savings' in family:
                    print(f"     💰 Savings: {family['savings']}")
        else:
            print(f"❌ Failed to get employee data: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing employee data: {e}")

if __name__ == '__main__':
    print("🚀 Testing Employee Group Management APIs")
    print("=" * 60)
    
    # Run tests in sequence
    test_get_existing_groups()
    test_add_employee_groups() 
    test_get_existing_groups()  # Check again after adding
    test_split_employee_group()
    test_employee_management_data()
    
    print("\n✅ All tests completed!")
