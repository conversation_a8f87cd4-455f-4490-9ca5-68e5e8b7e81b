#!/usr/bin/env python3
"""
Test script to demonstrate ROI% vs Dollar Savings calculations
"""

import yaml
from app import create_app
from models import Employee, User
from routes.advanced_api import calculate_job_family_roi

def test_roi_vs_savings():
    app = create_app()
    
    with app.app_context():
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Admin user not found")
            return
        
        # Load config
        with open('roi_config.yaml', 'r') as f:
            roi_config = yaml.safe_load(f)
        
        print("💰 ROI% vs Dollar Savings Comparison")
        print("=" * 60)
        print()
        
        # Test different adoption rates for Software Engineers
        job_family = "Software Engineer"
        adoption_rates = [0, 25, 50, 75, 100]
        
        print(f"📊 {job_family} - ROI% vs Dollar Savings by Adoption Rate")
        print("-" * 60)
        print(f"{'Adoption':<10} {'ROI%':<10} {'Annual Savings':<15} {'Monthly Savings':<15}")
        print("-" * 60)
        
        for adoption_rate in adoption_rates:
            roi_data = calculate_job_family_roi(job_family, adoption_rate, admin_user.id, roi_config)
            
            if isinstance(roi_data, dict):
                roi_pct = roi_data['roi_percentage']
                annual_savings = roi_data['annual_savings']
                monthly_savings = roi_data['monthly_savings']
                monthly_display = f"${monthly_savings:+,.0f}"
            else:
                roi_pct = roi_data
                annual_savings = "N/A"
                monthly_display = "N/A"
            
            print(f"{adoption_rate}%{'':<7} {roi_pct:<10} {annual_savings:<15} {monthly_display:<15}")
        
        print()
        print("🔍 Key Insights:")
        print("• 0% adoption = -100% ROI = Pure cost (negative savings)")
        print("• Higher adoption = Higher ROI% AND higher dollar savings")
        print("• Dollar savings show actual business impact")
        print("• ROI% shows efficiency of investment")
        print()
        
        # Compare different job families at 75% adoption
        print("📈 Job Family Comparison at 75% Adoption")
        print("-" * 60)
        print(f"{'Job Family':<20} {'ROI%':<10} {'Annual Savings':<15} {'Cost/User':<12}")
        print("-" * 60)
        
        employees = Employee.query.filter_by(user_id=admin_user.id).all()
        job_families = list(set(emp.job_family for emp in employees))
        ai_tool_costs = roi_config.get('ai_tool_costs', {})
        
        for job_family in sorted(job_families):
            roi_data = calculate_job_family_roi(job_family, 75, admin_user.id, roi_config)
            cost_per_user = ai_tool_costs.get(job_family, 50)
            
            if isinstance(roi_data, dict):
                roi_pct = roi_data['roi_percentage']
                annual_savings = roi_data['annual_savings']
            else:
                roi_pct = roi_data
                annual_savings = "N/A"
            
            print(f"{job_family:<20} {roi_pct:<10} {annual_savings:<15} ${cost_per_user}/mo")
        
        print()
        print("💡 Business Value Translation:")
        print("• ROI% = Investment efficiency (how much return per dollar spent)")
        print("• Annual Savings = Actual money saved/earned per year")
        print("• Negative savings = Net cost to the business")
        print("• Positive savings = Net profit from AI investment")
        print()
        
        # Calculate total company savings
        print("🏢 Total Company Impact at Current Adoption Rates")
        print("-" * 50)
        
        total_annual_savings = 0
        total_monthly_cost = 0
        
        for job_family in job_families:
            family_config = roi_config.get('job_family_roi', {}).get(job_family, {})
            current_adoption = family_config.get('base_ai_adoption', 70)
            
            roi_data = calculate_job_family_roi(job_family, current_adoption, admin_user.id, roi_config)
            
            if isinstance(roi_data, dict):
                annual_savings_raw = roi_data.get('annual_savings_raw', 0)
                total_annual_savings += annual_savings_raw
            
            # Calculate monthly cost
            family_employees = Employee.query.filter_by(user_id=admin_user.id, job_family=job_family).count()
            cost_per_user = ai_tool_costs.get(job_family, 50)
            total_monthly_cost += family_employees * cost_per_user
        
        total_annual_cost = total_monthly_cost * 12
        
        print(f"Total Annual AI Tool Costs: ${total_annual_cost:,}")
        print(f"Total Annual Productivity Value: ${total_annual_savings + total_annual_cost:,}")
        print(f"Net Annual Savings: ${total_annual_savings:+,}")
        
        if total_annual_cost > 0:
            company_roi = (total_annual_savings / total_annual_cost) * 100
            print(f"Company-wide ROI: {company_roi:+.0f}%")
        
        print()
        print("🎯 Summary:")
        if total_annual_savings > 0:
            print(f"✅ AI investment is PROFITABLE: ${total_annual_savings:,}/year savings")
        else:
            print(f"❌ AI investment is COSTING: ${abs(total_annual_savings):,}/year loss")

if __name__ == '__main__':
    test_roi_vs_savings()
