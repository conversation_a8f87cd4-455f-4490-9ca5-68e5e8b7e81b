#!/usr/bin/env python3
"""
Database migration script to add job_family column to Employee table
"""

import sqlite3
import os
from app import create_app
from models import db

def migrate_database():
    app = create_app()
    
    with app.app_context():
        # Get the database file path
        db_path = app.config.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///instance/database.db')
        if db_path.startswith('sqlite:///'):
            db_file = db_path.replace('sqlite:///', '')
        else:
            db_file = 'instance/database.db'
        
        print(f"Migrating database: {db_file}")
        
        # Check if database file exists
        if not os.path.exists(db_file):
            print("Database file doesn't exist. Creating new database...")
            db.create_all()
            print("New database created successfully!")
            return
        
        # Connect to SQLite database
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # Check if job_family column exists
            cursor.execute("PRAGMA table_info(employee)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'job_family' not in columns:
                print("Adding job_family column to employee table...")
                
                # Add the job_family column
                cursor.execute("ALTER TABLE employee ADD COLUMN job_family VARCHAR(100)")
                
                # Update existing employees with default job families based on employee_id
                cursor.execute("SELECT id, employee_id_str FROM employee")
                employees = cursor.fetchall()
                
                for emp_id, emp_id_str in employees:
                    # Determine job family from employee ID prefix
                    if emp_id_str.startswith('SDE'):
                        job_family = 'Software Engineer'
                    elif emp_id_str.startswith('PM'):
                        job_family = 'Product Manager'
                    elif emp_id_str.startswith('DS'):
                        job_family = 'Data Scientist'
                    elif emp_id_str.startswith('DES'):
                        job_family = 'Designer'
                    elif emp_id_str.startswith('RS'):
                        job_family = 'Research Scientist'
                    elif emp_id_str.startswith('EM'):
                        job_family = 'Engineering Manager'
                    elif emp_id_str.startswith('DO'):
                        job_family = 'DevOps Engineer'
                    else:
                        job_family = 'Software Engineer'  # Default
                    
                    cursor.execute("UPDATE employee SET job_family = ? WHERE id = ?", (job_family, emp_id))
                
                # Make job_family NOT NULL (SQLite doesn't support ALTER COLUMN, so we'll leave it nullable)
                print("Updated existing employees with job families")
                
            else:
                print("job_family column already exists")
            
            # Commit changes
            conn.commit()
            print("Database migration completed successfully!")
            
        except Exception as e:
            print(f"Error during migration: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

if __name__ == '__main__':
    migrate_database()
