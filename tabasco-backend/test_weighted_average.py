#!/usr/bin/env python3
"""
Test script to demonstrate weighted average salary calculation
"""

from app import create_app
from models import Employee, User

def test_weighted_average():
    app = create_app()
    
    with app.app_context():
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Admin user not found")
            return
        
        employees = Employee.query.filter_by(user_id=admin_user.id).all()
        if not employees:
            print("No employees found")
            return
        
        print(f"Testing weighted average calculation for {len(employees)} employees")
        print("=" * 60)
        
        # Define level weights
        level_weights = {
            'L1': 1, 'L2': 1, 'L3': 1, 'L4': 2, 'L5': 3, 
            'L6': 4, 'L7': 5, 'L8': 6, 'L9': 7, 'L10': 8
        }
        
        # Calculate simple average
        total_compensation = sum(emp.total_compensation for emp in employees)
        simple_avg = total_compensation / len(employees)
        
        # Calculate weighted average
        total_weighted_compensation = 0
        total_weights = 0
        level_stats = {}
        
        for emp in employees:
            weight = level_weights.get(emp.level, 1)
            total_weighted_compensation += emp.total_compensation * weight
            total_weights += weight
            
            if emp.level not in level_stats:
                level_stats[emp.level] = {'count': 0, 'total_salary': 0, 'weight': weight}
            level_stats[emp.level]['count'] += 1
            level_stats[emp.level]['total_salary'] += emp.total_compensation
        
        weighted_avg = total_weighted_compensation / total_weights
        
        print(f"Simple Average Salary: ${simple_avg:,.0f} (${simple_avg/1000:.0f}K)")
        print(f"Weighted Average Salary: ${weighted_avg:,.0f} (${weighted_avg/1000:.0f}K)")
        print(f"Difference: ${weighted_avg - simple_avg:,.0f} (${(weighted_avg - simple_avg)/1000:.0f}K)")
        print(f"Percentage difference: {((weighted_avg - simple_avg) / simple_avg) * 100:.1f}%")
        
        print("\nLevel Distribution and Impact:")
        print("-" * 60)
        print(f"{'Level':<6} {'Count':<6} {'Weight':<7} {'Avg Salary':<12} {'Impact'}")
        print("-" * 60)
        
        for level in sorted(level_stats.keys()):
            stats = level_stats[level]
            avg_salary = stats['total_salary'] / stats['count']
            impact = stats['count'] * stats['weight']
            print(f"{level:<6} {stats['count']:<6} {stats['weight']:<7} ${avg_salary/1000:.0f}K{'':<7} {impact}")
        
        print("-" * 60)
        print(f"Total weights: {total_weights}")
        
        print("\nWeighting Logic:")
        print("- Higher levels (L7, L8) get more weight in the average")
        print("- This reflects their higher impact on overall compensation costs")
        print("- Useful for budget planning and cost analysis")

if __name__ == '__main__':
    test_weighted_average()
