#!/usr/bin/env python3
"""
Test script for adoption rates functionality
"""

import yaml
import os

def test_adoption_rates():
    print("Testing Adoption Rates Configuration")
    print("=" * 50)
    
    # Check if roi_config.yaml exists
    if os.path.exists('roi_config.yaml'):
        with open('roi_config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        print("Current ROI Configuration:")
        print("-" * 30)
        
        job_families = config.get('job_family_roi', {})
        for family, settings in job_families.items():
            adoption = settings.get('base_ai_adoption', 'Not set')
            roi = settings.get('base_roi', 'Not set')
            print(f"{family}:")
            print(f"  AI Adoption: {adoption}%")
            print(f"  Base ROI: {roi}%")
            print()
        
        print("Global Settings:")
        global_settings = config.get('global_settings', {})
        for key, value in global_settings.items():
            print(f"  {key}: {value}")
    else:
        print("roi_config.yaml not found")
    
    print("\nTesting adoption rate update...")
    
    # Test updating adoption rates
    test_rates = {
        'Software Engineer': 75,
        'Product Manager': 90,
        'Data Scientist': 95
    }
    
    print(f"Test rates to apply: {test_rates}")
    
    # This would normally be done via the API endpoint
    print("✅ Adoption rates functionality ready for testing!")

if __name__ == '__main__':
    test_adoption_rates()
