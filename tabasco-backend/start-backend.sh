#!/bin/bash

# 🌶️ Tabasco Backend Startup Script
# This script sets up and starts the Flask backend server

set -e  # Exit on any error

echo "🌶️  Starting Tabasco Backend..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    print_error "app.py not found. Please run this script from the tabasco-backend directory."
    exit 1
fi

# 1. Create necessary directories
print_status "Creating necessary directories..."
mkdir -p instance
mkdir -p logs
print_success "Directories created"

# 2. Check if .env file exists, create from example if not
if [ ! -f ".env" ]; then
    print_warning ".env file not found"
    if [ -f ".env.example" ]; then
        print_status "Copying .env.example to .env..."
        cp .env.example .env
        print_success ".env file created from example"
    else
        print_error ".env.example not found. Please create .env file manually."
        exit 1
    fi
fi

# 3. Install/upgrade dependencies
print_status "Installing Python dependencies..."
if command -v pip3 &> /dev/null; then
    pip3 install -r requirements.txt
elif command -v pip &> /dev/null; then
    pip install -r requirements.txt
else
    print_error "pip not found. Please install Python pip."
    exit 1
fi
print_success "Dependencies installed"

# 4. Create default users (demo and admin) if they don't exist
print_status "Setting up database and default users..."
if [ -f "create_default_users.py" ]; then
    python3 create_default_users.py
    print_success "Default users setup completed"
elif [ -f "create_demo_user.py" ]; then
    print_warning "Using legacy demo user creation..."
    python3 create_demo_user.py
    print_success "Demo user setup completed"
else
    print_warning "No user creation script found, skipping user setup"
fi

# 5. Check if port 5004 is available
print_status "Checking if port 5004 is available..."
if lsof -Pi :5004 -sTCP:LISTEN -t >/dev/null ; then
    print_warning "Port 5004 is already in use. Attempting to kill existing process..."
    lsof -ti:5004 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 6. Start the Flask application
print_status "Starting Flask backend server..."
echo ""
print_success "🚀 Backend starting on http://localhost:5004"
print_success "📊 Default login credentials:"
print_success "   👤 Demo User: demo / demo123"
print_success "   🔐 Admin User: admin / password"
echo ""
print_status "Press Ctrl+C to stop the server"
echo "=================================="

# Start the application
if command -v python3 &> /dev/null; then
    python3 app.py
elif command -v python &> /dev/null; then
    python app.py
else
    print_error "Python not found. Please install Python."
    exit 1
fi
