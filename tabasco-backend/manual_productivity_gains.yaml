# Manual Productivity Gain Estimates for Software Development Engineers (SDE)
# User can define the estimated productivity gain percentage for each level.
# These are just example values and should be adjusted by the user.

L1:
  role: "SDE L1 (Junior Developer)"
  productivity_gain_percentage: 40 # e.g., 10% gain
  notes: "Focus on learning, simple tasks, code generation for boilerplate."

L2:
  role: "SDE L2 (Developer)"
  productivity_gain_percentage: 30
  notes: "More complex tasks, debugging assistance, code refactoring suggestions."

L3:
  role: "SDE L3 (Senior Developer)"
  productivity_gain_percentage: 20
  notes: "System design ideas, complex problem solving, documentation generation."

L4:
  role: "SDE L4 (Staff Engineer)"
  productivity_gain_percentage: 25
  notes: "Architectural insights, advanced research, mentoring leverage."

L5:
  role: "SDE L5 (Senior Staff Engineer)"
  productivity_gain_percentage: 20
  notes: "Strategic technical direction, innovation, cross-team impact."

L6:
  role: "SDE L6 (Principal Engineer)"
  productivity_gain_percentage: 15
  notes: "Broad technical leadership, defining new standards, industry influence."

L7:
  role: "SDE L7 (Senior Principal Engineer)"
  productivity_gain_percentage: 10 # Gains might plateau or shift focus
  notes: "Deep expertise, solving hardest problems, long-term vision."

L8:
  role: "SDE L8 (Distinguished Engineer)"
  productivity_gain_percentage: 5
  notes: "Company-wide impact, shaping future technology, external representation."

L9:
  role: "SDE L9 (Fellow)"
  productivity_gain_percentage: 5
  notes: "Pioneering new fields, exceptional and rare contributions."

L10:
  role: "SDE L10 (Senior Fellow)"
  productivity_gain_percentage: 5
  notes: "Highest technical echelon, transformative impact."
