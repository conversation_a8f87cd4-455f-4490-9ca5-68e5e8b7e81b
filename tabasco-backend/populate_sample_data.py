#!/usr/bin/env python3
"""
Sample data population script for Tabasco AI Tools Management
Populates the database with sample AI tools and employee data
"""

from app import create_app
from extensions import db
from models import User, Employee, AITool
from werkzeug.security import generate_password_hash
import yaml

def create_sample_user():
    """Create a sample user for testing"""
    user = User.query.filter_by(username='demo').first()
    if not user:
        user = User(
            username='demo',
            email='<EMAIL>',
            password_hash=generate_password_hash('demo123')
        )
        db.session.add(user)
        db.session.commit()
        print("✅ Created demo user (username: demo, password: demo123)")
    else:
        print("👤 Demo user already exists")
    return user

def create_sample_employees(user):
    """Create sample employees for the demo user"""
    # Check if employees already exist
    existing_employees = Employee.query.filter_by(user_id=user.id).count()
    if existing_employees > 0:
        print(f"👥 {existing_employees} employees already exist for demo user")
        return
    
    sample_employees = [
        # Software Engineers
        {'employee_id_str': 'SE001', 'job_family': 'Software Engineer', 'level': 'L5', 'tenure_in_role': 2, 'tenure_since_last_hire': 3, 'total_compensation': 180000},
        {'employee_id_str': 'SE002', 'job_family': 'Software Engineer', 'level': 'L6', 'tenure_in_role': 3, 'tenure_since_last_hire': 4, 'total_compensation': 220000},
        {'employee_id_str': 'SE003', 'job_family': 'Software Engineer', 'level': 'L4', 'tenure_in_role': 1, 'tenure_since_last_hire': 2, 'total_compensation': 150000},
        {'employee_id_str': 'SE004', 'job_family': 'Software Engineer', 'level': 'L7', 'tenure_in_role': 4, 'tenure_since_last_hire': 5, 'total_compensation': 280000},
        
        # Product Managers
        {'employee_id_str': 'PM001', 'job_family': 'Product Manager', 'level': 'L6', 'tenure_in_role': 2, 'tenure_since_last_hire': 3, 'total_compensation': 200000},
        {'employee_id_str': 'PM002', 'job_family': 'Product Manager', 'level': 'L5', 'tenure_in_role': 1, 'tenure_since_last_hire': 2, 'total_compensation': 170000},
        
        # Data Scientists
        {'employee_id_str': 'DS001', 'job_family': 'Data Scientist', 'level': 'L5', 'tenure_in_role': 2, 'tenure_since_last_hire': 3, 'total_compensation': 190000},
        {'employee_id_str': 'DS002', 'job_family': 'Data Scientist', 'level': 'L6', 'tenure_in_role': 3, 'tenure_since_last_hire': 4, 'total_compensation': 230000},
        
        # Designers
        {'employee_id_str': 'UX001', 'job_family': 'Designer', 'level': 'L5', 'tenure_in_role': 2, 'tenure_since_last_hire': 3, 'total_compensation': 160000},
        {'employee_id_str': 'UX002', 'job_family': 'Designer', 'level': 'L4', 'tenure_in_role': 1, 'tenure_since_last_hire': 2, 'total_compensation': 140000},
    ]
    
    for emp_data in sample_employees:
        employee = Employee(
            employee_id_str=emp_data['employee_id_str'],
            job_family=emp_data['job_family'],
            level=emp_data['level'],
            tenure_in_role=emp_data['tenure_in_role'],
            tenure_since_last_hire=emp_data['tenure_since_last_hire'],
            total_compensation=emp_data['total_compensation'],
            user_id=user.id
        )
        db.session.add(employee)
    
    db.session.commit()
    print(f"✅ Created {len(sample_employees)} sample employees")

def create_sample_ai_tools(user):
    """Create sample AI tools from scraper data"""
    # Check if tools already exist
    existing_tools = AITool.query.filter_by(user_id=user.id).count()
    if existing_tools > 0:
        print(f"🤖 {existing_tools} AI tools already exist for demo user")
        return
    
    # Load scraper data
    try:
        with open('genai_tool_pricing.yaml', 'r') as f:
            scraper_data = yaml.safe_load(f)
    except FileNotFoundError:
        print("⚠️  genai_tool_pricing.yaml not found, using fallback data")
        scraper_data = {}
    
    # Sample tools to add (subset of available tools)
    sample_tools = [
        {
            'key': 'ChatGPT-Plus',
            'name': 'OpenAI ChatGPT Plus',
            'provider': 'OpenAI',
            'category': 'General AI Assistant',
            'monthly_cost': 20,
            'user_count': 8,
            'status': 'active'
        },
        {
            'key': 'GitHub-Copilot-Business',
            'name': 'GitHub Copilot Business',
            'provider': 'Microsoft',
            'category': 'Code Generation',
            'monthly_cost': 19,
            'user_count': 4,  # Only software engineers
            'status': 'active'
        },
        {
            'key': 'Claude-Pro',
            'name': 'Anthropic Claude Pro',
            'provider': 'Anthropic',
            'category': 'General AI Assistant',
            'monthly_cost': 20,
            'user_count': 6,
            'status': 'inactive'  # Demonstrate inactive tool
        }
    ]
    
    for tool_data in sample_tools:
        # Use scraper data if available, otherwise use sample data
        scraper_tool = scraper_data.get(tool_data['key'], {})
        
        tool = AITool(
            name=scraper_tool.get('name', tool_data['name']),
            provider=scraper_tool.get('provider', tool_data['provider']),
            category=scraper_tool.get('category', tool_data['category']),
            monthly_cost=scraper_tool.get('price_per_license_usd_monthly', tool_data['monthly_cost']),
            usage_level='medium',
            efficiency_rating=0,  # Will be updated by users
            user_count=tool_data['user_count'],
            status=tool_data['status'],
            user_id=user.id
        )
        db.session.add(tool)
    
    db.session.commit()
    print(f"✅ Created {len(sample_tools)} sample AI tools")

def populate_sample_data():
    """Main function to populate all sample data"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🚀 Populating sample data...")
            
            # Create sample user
            user = create_sample_user()
            
            # Create sample employees
            create_sample_employees(user)
            
            # Create sample AI tools
            create_sample_ai_tools(user)
            
            print("\n🎉 Sample data population complete!")
            print("📊 You can now login with:")
            print("   Username: demo")
            print("   Password: demo123")
            
            return True
            
        except Exception as e:
            print(f"❌ Error populating sample data: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = populate_sample_data()
    
    if not success:
        print("\n💥 Sample data population failed!")
        print("🔧 Please check your database configuration")
