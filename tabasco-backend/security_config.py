"""
Security Configuration for Tabasco Backend
Comprehensive protection against common attacks
"""

import os
from flask import request, jsonify
from functools import wraps

class SecurityConfig:
    """Security configuration and middleware"""
    
    # Rate limiting configuration
    RATE_LIMITS = {
        'login': "5 per minute",  # Prevent brute force
        'api_calls': "100 per hour",  # General API rate limit
        'health_check': "60 per minute",  # Health checks
    }
    
    # Allowed origins (will be populated from environment)
    ALLOWED_ORIGINS = []
    
    # Suspicious user agents to monitor
    SUSPICIOUS_AGENTS = [
        'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 
        'python-requests', 'postman', 'insomnia', 'httpie'
    ]
    
    # Blocked IP patterns (can be extended)
    BLOCKED_IPS = []
    
    @staticmethod
    def init_security(app):
        """Initialize security configuration"""
        
        # Load allowed origins from environment
        cors_origins = os.environ.get('CORS_ORIGINS', '')
        SecurityConfig.ALLOWED_ORIGINS = [origin.strip() for origin in cors_origins.split(',') if origin.strip()]
        
        # Security headers middleware
        @app.after_request
        def add_security_headers(response):
            """Add comprehensive security headers"""
            headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
                'X-Robots-Tag': 'noindex, nofollow',
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
            
            for header, value in headers.items():
                response.headers[header] = value
            
            return response
        
        # Request monitoring middleware
        @app.before_request
        def monitor_requests():
            """Monitor and log suspicious requests"""
            
            # Skip monitoring for health checks
            if request.endpoint == 'health_check':
                return
            
            # Get request info
            ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent', '').lower()
            origin = request.headers.get('Origin', '')
            referer = request.headers.get('Referer', '')
            
            # Log suspicious activity
            if any(agent in user_agent for agent in SecurityConfig.SUSPICIOUS_AGENTS):
                print(f"⚠️ SECURITY: Suspicious request from {ip} - Agent: {user_agent}")
            
            # Check for blocked IPs
            if ip in SecurityConfig.BLOCKED_IPS:
                print(f"🚫 SECURITY: Blocked IP attempted access: {ip}")
                return jsonify({'error': 'Access denied'}), 403
            
            # Monitor for potential attacks
            if len(request.args) > 20:  # Too many query parameters
                print(f"⚠️ SECURITY: Excessive query parameters from {ip}")
            
            if request.content_length and request.content_length > 10 * 1024 * 1024:  # 10MB limit
                print(f"⚠️ SECURITY: Large request body from {ip}")
                return jsonify({'error': 'Request too large'}), 413

def require_api_key(f):
    """Decorator to require API key for sensitive operations"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        expected_key = os.environ.get('API_KEY')
        
        if expected_key and api_key != expected_key:
            return jsonify({'error': 'Invalid API key'}), 401
        
        return f(*args, **kwargs)
    return decorated_function

def log_security_event(event_type, details, ip=None):
    """Log security events for monitoring"""
    if not ip:
        ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    
    print(f"🔒 SECURITY EVENT: {event_type} - IP: {ip} - Details: {details}")

def is_safe_redirect_url(url):
    """Check if redirect URL is safe"""
    if not url:
        return False
    
    # Only allow relative URLs or URLs from allowed origins
    if url.startswith('/'):
        return True
    
    for origin in SecurityConfig.ALLOWED_ORIGINS:
        if url.startswith(origin):
            return True
    
    return False

# Security decorators for different protection levels
def rate_limit_login(f):
    """Rate limit for login attempts"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # This will be handled by Flask-Limiter in the route
        return f(*args, **kwargs)
    return decorated_function

def monitor_failed_logins(f):
    """Monitor and log failed login attempts"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        result = f(*args, **kwargs)
        
        # If login failed, log the attempt
        if hasattr(result, 'status_code') and result.status_code == 401:
            ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            username = request.json.get('username', 'unknown') if request.json else 'unknown'
            log_security_event('FAILED_LOGIN', f'Username: {username}', ip)
        
        return result
    return decorated_function

# IP-based security functions
def add_to_blocklist(ip, reason="Manual block"):
    """Add IP to blocklist"""
    if ip not in SecurityConfig.BLOCKED_IPS:
        SecurityConfig.BLOCKED_IPS.append(ip)
        log_security_event('IP_BLOCKED', f'Reason: {reason}', ip)

def remove_from_blocklist(ip):
    """Remove IP from blocklist"""
    if ip in SecurityConfig.BLOCKED_IPS:
        SecurityConfig.BLOCKED_IPS.remove(ip)
        log_security_event('IP_UNBLOCKED', 'Manual unblock', ip)

# Content Security Policy
CSP_POLICY = (
    "default-src 'self'; "
    "script-src 'self' 'unsafe-inline'; "
    "style-src 'self' 'unsafe-inline'; "
    "img-src 'self' data: https:; "
    "font-src 'self'; "
    "connect-src 'self' https:; "
    "frame-ancestors 'none';"
)

def add_csp_header(response):
    """Add Content Security Policy header"""
    response.headers['Content-Security-Policy'] = CSP_POLICY
    return response
