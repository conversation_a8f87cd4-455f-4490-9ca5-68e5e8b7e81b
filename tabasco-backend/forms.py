from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, IntegerField, SelectField
from wtforms.validators import <PERSON>Required, Email, EqualTo, ValidationError, Length
from models import User # Changed from .models

class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=2, max=80)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Register')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('That username is taken. Please choose a different one.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('That email is already registered. Please choose a different one or login.')

class LoginForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Login')

class EmployeeForm(FlaskForm):
    employee_id_str = StringField('Employee ID', validators=[DataRequired(), Length(max=50)])
    tenure_in_role = IntegerField('Tenure in Role (Years)', validators=[DataRequired()])
    level_choices = [(f'L{i}', f'L{i}') for i in range(1, 11)]
    level = SelectField('Level', choices=level_choices, validators=[DataRequired()])
    tenure_since_last_hire = IntegerField('Tenure Since Last Hire (Years)', validators=[DataRequired()])
    total_compensation = IntegerField('Total Compensation (Annual USD)', validators=[DataRequired()])
    submit = SubmitField('Save Employee')

# Form for updating productivity gains - might be useful later
class ProductivityGainForm(FlaskForm):
    # Dynamically create fields for each level L1-L10
    # Example for L1, repeat for L2-L10
    # l1_gain = IntegerField('L1 Productivity Gain (%)', validators=[DataRequired()])
    submit = SubmitField('Update Gains')

    def __init__(self, *args, **kwargs):
        super(ProductivityGainForm, self).__init__(*args, **kwargs)
        for i in range(1, 11):
            field_name = f'l{i}_gain'
            label = f'L{i} Productivity Gain (%)'
            setattr(self, field_name, IntegerField(label, validators=[DataRequired()]))
            # To access this field in the template: form.l1_gain, form.l2_gain etc.
            # Or iterate through form._fields if needed in a loop
