# Tabasco AI Tools Data Persistence

## Overview
This document explains how AI Tools data is persisted and managed in the Tabasco application.

## Database Schema

### AITool Model
```python
class AITool(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)           # Tool name (e.g., "ChatGPT Plus")
    provider = db.Column(db.String(50), nullable=False)        # Provider (e.g., "OpenAI")
    category = db.Column(db.String(50), nullable=False)        # Category (e.g., "General AI")
    monthly_cost = db.Column(db.Float, nullable=False)         # Per-license cost per month
    usage_level = db.Column(db.String(20), default='medium')   # Usage intensity
    efficiency_rating = db.Column(db.Float, default=0)         # User-defined efficiency %
    user_count = db.Column(db.Integer, default=0)              # Number of active users
    status = db.Column(db.String(20), default='active')        # active/inactive
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # Owner
```

## What Gets Persisted vs Calculated

### Persisted Data (Stored in Database)
- ✅ **Tool Name**: "OpenAI ChatGPT Plus"
- ✅ **Provider**: "OpenAI" 
- ✅ **Category**: "General AI Assistant"
- ✅ **Per-License Cost**: $20/month
- ✅ **User Count**: 8 active users
- ✅ **Status**: active/inactive
- ✅ **Efficiency Rating**: User-defined percentage
- ✅ **Usage Level**: low/medium/high
- ✅ **Timestamps**: created_at, updated_at

### Calculated Data (Computed on Demand)
- 🧮 **Total Monthly Cost**: per_license_cost × user_count
- 🧮 **Annual Cost**: total_monthly_cost × 12
- 🧮 **Company Stats**: Total spend, adoption rates, etc.
- 🧮 **ROI Metrics**: Based on employee data and tool usage

## API Endpoints

### GET /api/ai-tools
Returns all tools with calculated fields:
```json
{
  "id": 1,
  "name": "ChatGPT Plus",
  "provider": "OpenAI",
  "monthly_cost": 20,           // Per-license cost (persisted)
  "user_count": 8,              // Active users (persisted)
  "status": "active",           // Status (persisted)
  "total_monthly_cost": 160,    // Calculated: 20 × 8
  "is_persisted": true          // Indicates saved data
}
```

### POST /api/ai-tools
Creates new tool with validation:
```json
{
  "name": "Custom AI Tool",
  "provider": "Custom Provider",
  "category": "Custom Tool",
  "monthly_cost": 25,           // Per-license cost
  "user_count": 5,              // Initial user count
  "status": "active"
}
```

### PUT /api/ai-tools/{id}
Updates existing tool (all fields optional):
```json
{
  "user_count": 10,             // Update active users
  "efficiency_rating": 15,      // Update efficiency
  "status": "inactive"          // Toggle status
}
```

## Setup Instructions

### 1. Initialize Database
```bash
cd tabasco-backend
python init_db.py
```

### 2. Populate Sample Data
```bash
python populate_sample_data.py
```

### 3. Complete Setup (Both Steps)
```bash
python setup_tabasco.py
```

## Sample Data

### Demo User
- Username: `demo`
- Password: `demo123`

### Sample Tools
1. **ChatGPT Plus** - $20/month, 8 users, active
2. **GitHub Copilot** - $19/month, 4 users, active  
3. **Claude Pro** - $20/month, 6 users, inactive

### Sample Employees
- 4 Software Engineers (L4-L7)
- 2 Product Managers (L5-L6)
- 2 Data Scientists (L5-L6)
- 2 Designers (L4-L5)

## Data Flow

### Adding New Tool
1. User selects tool from catalog or creates custom
2. Frontend sends POST to `/api/ai-tools`
3. Backend validates and persists to database
4. Returns tool with calculated total cost
5. Frontend updates UI with new tool

### Updating User Count
1. User edits user count in table
2. Frontend sends PUT to `/api/ai-tools/{id}`
3. Backend updates user_count field
4. Recalculates total_monthly_cost
5. Stats API recalculates company totals
6. Frontend refreshes stats display

### Toggling Tool Status
1. User toggles active/inactive switch
2. Backend updates status field
3. Calculated costs become 0 for inactive tools
4. Company stats exclude inactive tools
5. UI shows $0 for inactive tools

## Benefits of This Approach

### Data Integrity
- Core tool data is safely persisted
- No risk of losing tool configurations
- Audit trail with timestamps

### Performance
- Fast calculations on demand
- No need to store redundant calculated values
- Real-time updates without data inconsistency

### Flexibility
- Easy to change calculation logic
- Can add new calculated fields without migration
- User modifications persist across sessions

### Scalability
- Minimal database storage
- Efficient queries
- Easy to backup and restore
