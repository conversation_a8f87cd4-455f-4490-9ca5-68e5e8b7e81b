from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
# SQLAlchemy and LoginManager are now in extensions.py
import os
from dotenv import load_dotenv
from extensions import db, login_manager # Import from extensions

# Load environment variables from .env file
load_dotenv()

def create_app(config_name=None):
    app = Flask(__name__, instance_relative_config=True)

    # Determine configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    # Load configuration based on environment
    if config_name == 'production':
        from production_config import ProductionConfig
        app.config.from_object(ProductionConfig)
        ProductionConfig.init_app(app)
    else:
        app.config.from_object('config.Config')

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login' # Route for login page

    # Enable CORS for React frontend with comprehensive settings
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Access-Control-Allow-Credentials'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Initialize Rate Limiting
    limiter = Limiter(
        key_func=get_remote_address,
        default_limits=["1000 per day", "100 per hour", "10 per minute"],
        storage_uri="memory://",
    )
    limiter.init_app(app)

    # Security middleware
    @app.before_request
    def security_headers():
        # Block requests from suspicious origins (except health checks)
        if request.endpoint not in ['health_check', 'serve_frontend', 'serve_static_files']:
            # Check for common attack patterns
            user_agent = request.headers.get('User-Agent', '').lower()
            suspicious_agents = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']

            # Allow legitimate requests but log suspicious ones
            if any(agent in user_agent for agent in suspicious_agents):
                print(f"⚠️ Suspicious request from: {get_remote_address()} - {user_agent}")
                # Don't block completely, just log for now

    @app.after_request
    def add_security_headers(response):
        # Add comprehensive security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['X-Robots-Tag'] = 'noindex, nofollow'  # Prevent search engine indexing
        return response

    # Import models here for user_loader
    from models import User

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Import and register blueprints
    # Blueprints might import db or login_manager from this 'app' module
    # Deferring blueprint import until after db.create_all()

    with app.app_context():
        db.create_all() # Create database tables

        # Initialize default users and data for production
        from models import User, Employee, AITool

        # Create users from environment variables
        created_users = []

        # Admin user
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
            admin_password = os.environ.get('ADMIN_PASSWORD')
            admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')

            if admin_password:
                print("🔧 Creating admin user from environment variables...")
                from werkzeug.security import generate_password_hash
                admin_user = User(
                    username=admin_username,
                    email=admin_email,
                    password_hash=generate_password_hash(admin_password)
                )
                db.session.add(admin_user)
                created_users.append(admin_user)
                print(f"✅ Admin user created: {admin_username}")
            else:
                print("⚠️ No ADMIN_PASSWORD environment variable set - skipping admin user creation")

        # Demo user
        demo_user = None
        demo_username = os.environ.get('DEMO_USERNAME')
        demo_password = os.environ.get('DEMO_PASSWORD')
        demo_email = os.environ.get('DEMO_EMAIL', '<EMAIL>')

        if demo_username and demo_password:
            existing_demo = User.query.filter_by(username=demo_username).first()
            if not existing_demo:
                print("🔧 Creating demo user from environment variables...")
                from werkzeug.security import generate_password_hash
                demo_user = User(
                    username=demo_username,
                    email=demo_email,
                    password_hash=generate_password_hash(demo_password)
                )
                db.session.add(demo_user)
                created_users.append(demo_user)
                print(f"✅ Demo user created: {demo_username}")
            else:
                demo_user = existing_demo
                print(f"ℹ️ Demo user already exists: {demo_username}")
        else:
            print("ℹ️ No DEMO_USERNAME/DEMO_PASSWORD set - skipping demo user creation")
            print("ℹ️ Set DEMO_USERNAME and DEMO_PASSWORD to create demo account")

        # Create sample data for users
        if Employee.query.count() == 0 and (admin_user or demo_user):
            print("🔧 Creating sample employees...")

            # Determine which user to associate data with
            primary_user = admin_user if admin_user else demo_user

            # Admin user gets full dataset
            admin_employees = []
            if admin_user:
                admin_employees = [
                    Employee(
                        employee_id_str="ADM_DES001",
                        job_family="Designer",
                        level="L5",
                        tenure_in_role=2,
                        tenure_since_last_hire=3,
                        total_compensation=75000,
                        user_id=admin_user.id
                    ),
                    Employee(
                        employee_id_str="ADM_FSE001",
                        job_family="Full Stack Engineer",
                        level="L6",
                        tenure_in_role=3,
                        tenure_since_last_hire=4,
                        total_compensation=95000,
                        user_id=admin_user.id
                    ),
                    Employee(
                        employee_id_str="ADM_SDS001",
                        job_family="Senior Data Scientist",
                        level="L7",
                        tenure_in_role=4,
                        tenure_since_last_hire=5,
                        total_compensation=120000,
                        user_id=admin_user.id
                    ),
                ]

            # Demo user gets smaller dataset
            demo_employees = []
            if demo_user:
                demo_employees = [
                    Employee(
                        employee_id_str="DEMO_DES001",
                        job_family="Designer",
                        level="L4",
                        tenure_in_role=1,
                        tenure_since_last_hire=2,
                        total_compensation=65000,
                        user_id=demo_user.id
                    ),
                Employee(
                    employee_id_str="FSE001",
                    job_family="Full Stack Engineer",
                    level="L6",
                    tenure_in_role=3,
                    tenure_since_last_hire=4,
                    total_compensation=95000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="SDS001",
                    job_family="Senior Data Scientist",
                    level="L7",
                    tenure_in_role=4,
                    tenure_since_last_hire=5,
                    total_compensation=120000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="DES002",
                    job_family="Designer",
                    level="L4",
                    tenure_in_role=1,
                    tenure_since_last_hire=2,
                    total_compensation=65000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="FSE002",
                    job_family="Full Stack Engineer",
                    level="L5",
                    tenure_in_role=2,
                    tenure_since_last_hire=3,
                    total_compensation=85000,
                    user_id=admin_user.id
                ),
            ]
            # Add admin employees
            for emp in admin_employees:
                db.session.add(emp)

            # Add demo employees
            for emp in demo_employees:
                db.session.add(emp)

            total_employees = len(admin_employees) + len(demo_employees)
            print(f"✅ Created {total_employees} sample employees ({len(admin_employees)} admin, {len(demo_employees)} demo)")

        # Create sample AI tools for users
        if AITool.query.count() == 0 and (admin_user or demo_user):
            print("🔧 Creating sample AI tools...")

            # Admin user tools
            admin_tools = []
            if admin_user:
                admin_tools = [
                    AITool(
                        name="OpenAI ChatGPT Plus",
                        provider="OpenAI",
                        category="Language Model",
                        monthly_cost=20.0,
                        user_count=5,
                        usage_level="high",
                        efficiency_rating=4.5,
                        user_id=admin_user.id
                    ),
                    AITool(
                        name="GitHub Copilot",
                        provider="GitHub",
                        category="Code Assistant",
                        monthly_cost=10.0,
                        user_count=3,
                        usage_level="medium",
                        efficiency_rating=4.2,
                        user_id=admin_user.id
                    ),
                ]

            # Demo user tools
            demo_tools = []
            if demo_user:
                demo_tools = [
                    AITool(
                        name="Claude Pro",
                        provider="Anthropic",
                        category="Language Model",
                        monthly_cost=20.0,
                        user_count=2,
                        usage_level="medium",
                        efficiency_rating=4.3,
                        user_id=demo_user.id
                    ),
                    AITool(
                        name="Midjourney",
                        provider="Midjourney",
                        category="Image Generation",
                        monthly_cost=30.0,
                        user_count=1,
                        usage_level="low",
                        efficiency_rating=4.0,
                        user_id=demo_user.id
                    ),
                ]

            # Add all tools to database
            all_tools = admin_tools + demo_tools
            for tool in all_tools:
                db.session.add(tool)
            print(f"✅ Created {len(all_tools)} sample AI tools ({len(admin_tools)} admin, {len(demo_tools)} demo)")

        # Commit all changes
        try:
            db.session.commit()
            print("✅ Database initialization completed successfully")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            db.session.rollback()

    # Add health check endpoint for Digital Ocean
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'service': 'tabasco-backend'}, 200

    # Serve static frontend files (for single-service deployment)
    @app.route('/')
    def serve_frontend():
        return app.send_static_file('index.html')

    @app.route('/<path:path>')
    def serve_static_files(path):
        try:
            return app.send_static_file(path)
        except:
            # For SPA routing, return index.html for unknown routes
            return app.send_static_file('index.html')

    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.api import api_bp
    from routes.advanced_api import advanced_api
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(advanced_api)

    return app

if __name__ == '__main__':
    # Debug environment variables
    print("🔍 Environment Variables:")
    print(f"  FLASK_ENV: {os.environ.get('FLASK_ENV', 'NOT_SET')}")
    print(f"  PORT: {os.environ.get('PORT', 'NOT_SET')}")
    print(f"  HOST: {os.environ.get('HOST', 'NOT_SET')}")
    print(f"  DEBUG: {os.environ.get('DEBUG', 'NOT_SET')}")

    # Get environment from environment variable
    env = os.environ.get('FLASK_ENV', 'development')
    app = create_app(env)

    # Force production settings for Digital Ocean
    if env == 'production':
        host = '0.0.0.0'  # Always bind to all interfaces in production
        port = int(os.environ.get('PORT', 8080))  # Use Digital Ocean's PORT
        debug = False  # Never debug in production
    else:
        # Development settings
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5004))
        debug = True

    print(f"🌶️ Starting Tabasco on {host}:{port} (env: {env}, debug: {debug})")
    app.run(host=host, port=port, debug=debug)
