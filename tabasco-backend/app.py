from flask import Flask
from flask_cors import CORS
# SQLAlchemy and LoginManager are now in extensions.py
import os
from dotenv import load_dotenv
from extensions import db, login_manager # Import from extensions

# Load environment variables from .env file
load_dotenv()

def create_app():
    app = Flask(__name__, instance_relative_config=True)

    # Load configuration
    app.config.from_object('config.Config')

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login' # Route for login page

    # Enable CORS for React frontend with comprehensive settings
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Access-Control-Allow-Credentials'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Import models here for user_loader
    from models import User

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Import and register blueprints
    # Blueprints might import db or login_manager from this 'app' module
    # Deferring blueprint import until after db.create_all()

    with app.app_context():
        db.create_all() # Create database tables

    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.api import api_bp
    from routes.advanced_api import advanced_api
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(advanced_api)

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
