from flask import Flask
from flask_cors import CORS
# SQLAlchemy and LoginManager are now in extensions.py
import os
from dotenv import load_dotenv
from extensions import db, login_manager # Import from extensions

# Load environment variables from .env file
load_dotenv()

def create_app(config_name=None):
    app = Flask(__name__, instance_relative_config=True)

    # Determine configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    # Load configuration based on environment
    if config_name == 'production':
        from production_config import ProductionConfig
        app.config.from_object(ProductionConfig)
        ProductionConfig.init_app(app)
    else:
        app.config.from_object('config.Config')

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login' # Route for login page

    # Enable CORS for React frontend with comprehensive settings
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Access-Control-Allow-Credentials'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Import models here for user_loader
    from models import User

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Import and register blueprints
    # Blueprints might import db or login_manager from this 'app' module
    # Deferring blueprint import until after db.create_all()

    with app.app_context():
        db.create_all() # Create database tables

    # Add health check endpoint for Digital Ocean
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'service': 'tabasco-backend'}, 200

    # Serve static frontend files (for single-service deployment)
    @app.route('/')
    def serve_frontend():
        return app.send_static_file('index.html')

    @app.route('/<path:path>')
    def serve_static_files(path):
        try:
            return app.send_static_file(path)
        except:
            # For SPA routing, return index.html for unknown routes
            return app.send_static_file('index.html')

    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.api import api_bp
    from routes.advanced_api import advanced_api
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(advanced_api)

    return app

if __name__ == '__main__':
    # Debug environment variables
    print("🔍 Environment Variables:")
    print(f"  FLASK_ENV: {os.environ.get('FLASK_ENV', 'NOT_SET')}")
    print(f"  PORT: {os.environ.get('PORT', 'NOT_SET')}")
    print(f"  HOST: {os.environ.get('HOST', 'NOT_SET')}")
    print(f"  DEBUG: {os.environ.get('DEBUG', 'NOT_SET')}")

    # Get environment from environment variable
    env = os.environ.get('FLASK_ENV', 'development')
    app = create_app(env)

    # Force production settings for Digital Ocean
    if env == 'production':
        host = '0.0.0.0'  # Always bind to all interfaces in production
        port = int(os.environ.get('PORT', 8080))  # Use Digital Ocean's PORT
        debug = False  # Never debug in production
    else:
        # Development settings
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5004))
        debug = True

    print(f"🌶️ Starting Tabasco on {host}:{port} (env: {env}, debug: {debug})")
    app.run(host=host, port=port, debug=debug)
