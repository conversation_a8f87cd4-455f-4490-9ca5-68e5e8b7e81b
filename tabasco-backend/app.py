from flask import Flask
from flask_cors import CORS
# SQLAlchemy and LoginManager are now in extensions.py
import os
from dotenv import load_dotenv
from extensions import db, login_manager # Import from extensions

# Load environment variables from .env file
load_dotenv()

def create_app(config_name=None):
    app = Flask(__name__, instance_relative_config=True)

    # Determine configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    # Load configuration based on environment
    if config_name == 'production':
        from production_config import ProductionConfig
        app.config.from_object(ProductionConfig)
        ProductionConfig.init_app(app)
    else:
        app.config.from_object('config.Config')

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login' # Route for login page

    # Enable CORS for React frontend with comprehensive settings
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Access-Control-Allow-Credentials'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Import models here for user_loader
    from models import User

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Import and register blueprints
    # Blueprints might import db or login_manager from this 'app' module
    # Deferring blueprint import until after db.create_all()

    with app.app_context():
        db.create_all() # Create database tables

    # Add health check endpoint for Digital Ocean
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'service': 'tabasco-backend'}, 200

    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.api import api_bp
    from routes.advanced_api import advanced_api
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(advanced_api)

    return app

if __name__ == '__main__':
    # Get environment from environment variable
    env = os.environ.get('FLASK_ENV', 'development')
    app = create_app(env)

    # For production, use environment variables for host/port
    host = os.environ.get('HOST', app.config.get('HOST', '0.0.0.0'))
    port = int(os.environ.get('PORT', app.config.get('PORT', 5004)))
    debug = app.config.get('DEBUG', False)

    app.run(host=host, port=port, debug=debug)
