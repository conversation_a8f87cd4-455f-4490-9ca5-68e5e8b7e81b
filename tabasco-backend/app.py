from flask import Flask
from flask_cors import CORS
# SQLAlchemy and LoginManager are now in extensions.py
import os
from dotenv import load_dotenv
from extensions import db, login_manager # Import from extensions

# Load environment variables from .env file
load_dotenv()

def create_app(config_name=None):
    app = Flask(__name__, instance_relative_config=True)

    # Determine configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    # Load configuration based on environment
    if config_name == 'production':
        from production_config import ProductionConfig
        app.config.from_object(ProductionConfig)
        ProductionConfig.init_app(app)
    else:
        app.config.from_object('config.Config')

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login' # Route for login page

    # Enable CORS for React frontend with comprehensive settings
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Access-Control-Allow-Credentials'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Import models here for user_loader
    from models import User

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Import and register blueprints
    # Blueprints might import db or login_manager from this 'app' module
    # Deferring blueprint import until after db.create_all()

    with app.app_context():
        db.create_all() # Create database tables

        # Initialize default users and data for production
        from models import User, Employee, AITool

        # Check if admin user exists, if not create it
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            # Use environment variables for secure credentials
            admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
            admin_password = os.environ.get('ADMIN_PASSWORD')
            admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')

            if admin_password:
                print("🔧 Creating admin user from environment variables...")
                from werkzeug.security import generate_password_hash
                admin_user = User(
                    username=admin_username,
                    email=admin_email,
                    password_hash=generate_password_hash(admin_password)
                )
                db.session.add(admin_user)
                print(f"✅ Admin user created: {admin_username}")
            else:
                print("⚠️ No ADMIN_PASSWORD environment variable set - skipping admin user creation")
                print("⚠️ Set ADMIN_PASSWORD environment variable to create admin user")

        # Check if sample employees exist, if not create them
        if Employee.query.count() == 0:
            print("🔧 Creating sample employees...")
            sample_employees = [
                Employee(
                    employee_id_str="DES001",
                    job_family="Designer",
                    level="L5",
                    tenure_in_role=2,
                    tenure_since_last_hire=3,
                    total_compensation=75000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="FSE001",
                    job_family="Full Stack Engineer",
                    level="L6",
                    tenure_in_role=3,
                    tenure_since_last_hire=4,
                    total_compensation=95000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="SDS001",
                    job_family="Senior Data Scientist",
                    level="L7",
                    tenure_in_role=4,
                    tenure_since_last_hire=5,
                    total_compensation=120000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="DES002",
                    job_family="Designer",
                    level="L4",
                    tenure_in_role=1,
                    tenure_since_last_hire=2,
                    total_compensation=65000,
                    user_id=admin_user.id
                ),
                Employee(
                    employee_id_str="FSE002",
                    job_family="Full Stack Engineer",
                    level="L5",
                    tenure_in_role=2,
                    tenure_since_last_hire=3,
                    total_compensation=85000,
                    user_id=admin_user.id
                ),
            ]
            for emp in sample_employees:
                db.session.add(emp)
            print(f"✅ Created {len(sample_employees)} sample employees")

        # Check if AI tools exist, if not create them
        if AITool.query.count() == 0:
            print("🔧 Creating sample AI tools...")
            sample_tools = [
                AITool(
                    name="OpenAI ChatGPT Plus",
                    provider="OpenAI",
                    category="Language Model",
                    monthly_cost=20.0,
                    user_count=5,
                    usage_level="high",
                    efficiency_rating=4.5,
                    user_id=admin_user.id
                ),
                AITool(
                    name="GitHub Copilot",
                    provider="GitHub",
                    category="Code Assistant",
                    monthly_cost=10.0,
                    user_count=3,
                    usage_level="medium",
                    efficiency_rating=4.2,
                    user_id=admin_user.id
                ),
                AITool(
                    name="Claude Pro",
                    provider="Anthropic",
                    category="Language Model",
                    monthly_cost=20.0,
                    user_count=2,
                    usage_level="medium",
                    efficiency_rating=4.3,
                    user_id=admin_user.id
                ),
            ]
            for tool in sample_tools:
                db.session.add(tool)
            print(f"✅ Created {len(sample_tools)} sample AI tools")

        # Commit all changes
        try:
            db.session.commit()
            print("✅ Database initialization completed successfully")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            db.session.rollback()

    # Add health check endpoint for Digital Ocean
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'service': 'tabasco-backend'}, 200

    # Serve static frontend files (for single-service deployment)
    @app.route('/')
    def serve_frontend():
        return app.send_static_file('index.html')

    @app.route('/<path:path>')
    def serve_static_files(path):
        try:
            return app.send_static_file(path)
        except:
            # For SPA routing, return index.html for unknown routes
            return app.send_static_file('index.html')

    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.api import api_bp
    from routes.advanced_api import advanced_api
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(advanced_api)

    return app

if __name__ == '__main__':
    # Debug environment variables
    print("🔍 Environment Variables:")
    print(f"  FLASK_ENV: {os.environ.get('FLASK_ENV', 'NOT_SET')}")
    print(f"  PORT: {os.environ.get('PORT', 'NOT_SET')}")
    print(f"  HOST: {os.environ.get('HOST', 'NOT_SET')}")
    print(f"  DEBUG: {os.environ.get('DEBUG', 'NOT_SET')}")

    # Get environment from environment variable
    env = os.environ.get('FLASK_ENV', 'development')
    app = create_app(env)

    # Force production settings for Digital Ocean
    if env == 'production':
        host = '0.0.0.0'  # Always bind to all interfaces in production
        port = int(os.environ.get('PORT', 8080))  # Use Digital Ocean's PORT
        debug = False  # Never debug in production
    else:
        # Development settings
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5004))
        debug = True

    print(f"🌶️ Starting Tabasco on {host}:{port} (env: {env}, debug: {debug})")
    app.run(host=host, port=port, debug=debug)
