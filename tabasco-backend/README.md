# 🌶️ Tabasco Backend

Flask API backend for the Tabasco GenAI ROI Calculator.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip

### Installation

1. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Initialize database:**
```bash
python create_user.py
```

4. **Run the server:**
```bash
python app.py
```

The API will be available at `http://localhost:5004`

## 📋 API Endpoints

### Authentication
- `POST /api/login` - User login
- `GET /api/user` - Get current user info
- `POST /api/logout` - User logout

### ROI Calculation
- `POST /api/calculate-roi` - Calculate ROI for selected tools and levels
- `GET /api/dashboard-data` - Get dashboard data

## 🔧 Configuration

The app runs on port 5004 by default. CORS is configured to allow requests from:
- `http://localhost:3000` (React dev server)
- `http://localhost:3003` (Alternative React port)

## 🗃️ Database

Uses SQLite database stored in `instance/app.db`. Default user:
- Email: `<EMAIL>`
- Password: `password`

## 🛠️ Development

To run in development mode with auto-reload:
```bash
export FLASK_ENV=development
python app.py
```
