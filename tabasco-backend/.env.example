# Backend Environment Configuration Example
# Copy this file to .env and update the values as needed

# Server Configuration
HOST=127.0.0.1
PORT=5004
DEBUG=True

# Security (CHANGE THIS IN PRODUCTION!)
SECRET_KEY=your_secret_key_here

# Database Configuration
# For SQLite (default): sqlite:///instance/app.db
# For PostgreSQL: postgresql://user:password@localhost/dbname
# For MySQL: mysql://user:password@localhost/dbname
DATABASE_URL=sqlite:///tabasco.db

# CORS Configuration (comma-separated list of allowed origins)
# Add your frontend URLs here
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:3006,http://localhost:3007
