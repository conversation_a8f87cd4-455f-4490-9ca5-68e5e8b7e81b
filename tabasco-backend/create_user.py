from app import create_app
from extensions import db
from models import User
from werkzeug.security import generate_password_hash

def create_default_user():
    app = create_app()
    with app.app_context():
        # Check if user already exists
        existing_user = User.query.filter_by(username='admin').first()
        if existing_user:
            print("User 'admin' already exists")
            return
        
        # Create default user
        hashed_password = generate_password_hash('password')
        user = User(
            username='admin',
            email='<EMAIL>',
            password_hash=hashed_password
        )
        
        db.session.add(user)
        db.session.commit()
        print("Default user created successfully!")
        print("Username: admin")
        print("Password: password")
        print("Email: <EMAIL>")

if __name__ == '__main__':
    create_default_user()
