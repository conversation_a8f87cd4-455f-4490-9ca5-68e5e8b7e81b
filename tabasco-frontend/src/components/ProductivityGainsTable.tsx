import React from 'react';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { TrendingUp, Info } from 'lucide-react';

interface ProductivityGain {
  role: string;
  productivity_gain_percentage: number;
  notes: string;
}

interface ProductivityGainsTableProps {
  gains: Record<string, ProductivityGain>;
}

const ProductivityGainsTable: React.FC<ProductivityGainsTableProps> = ({ gains }) => {
  const getGainVariant = (percentage: number) => {
    if (percentage >= 30) return 'success';
    if (percentage >= 20) return 'warning';
    return 'secondary';
  };

  const getGainColor = (percentage: number) => {
    if (percentage >= 30) return 'text-green-600';
    if (percentage >= 20) return 'text-yellow-600';
    return 'text-blue-600';
  };

  const gainEntries = Object.entries(gains);

  if (gainEntries.length === 0) {
    return (
      <div className="text-center py-8">
        <Info className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
        <p className="text-muted-foreground">No productivity gains data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Role Level</TableHead>
              <TableHead>Productivity Gain</TableHead>
              <TableHead>Progress</TableHead>
              <TableHead>Notes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {gainEntries.map(([roleKey, gain]) => (
              <TableRow key={roleKey}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-semibold capitalize">{gain.role}</div>
                      <div className="text-xs text-muted-foreground">{roleKey}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Badge variant={getGainVariant(gain.productivity_gain_percentage) as any}>
                      {gain.productivity_gain_percentage}%
                    </Badge>
                    <span className={`text-sm font-medium ${getGainColor(gain.productivity_gain_percentage)}`}>
                      +{gain.productivity_gain_percentage}% efficiency
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-2">
                    <Progress 
                      value={Math.min(gain.productivity_gain_percentage, 50)} 
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {gain.productivity_gain_percentage}% of 50% max
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-sm">
                    <p className="text-sm text-muted-foreground">
                      {gain.notes}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="flex items-center space-x-2">
          <Badge variant="success" className="w-3 h-3 p-0"></Badge>
          <span className="text-muted-foreground">High Impact (≥30%)</span>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="warning" className="w-3 h-3 p-0"></Badge>
          <span className="text-muted-foreground">Medium Impact (20-29%)</span>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="w-3 h-3 p-0"></Badge>
          <span className="text-muted-foreground">Low Impact (&lt;20%)</span>
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        <p>
          Average productivity gain: {' '}
          <span className="font-medium">
            {gainEntries.length > 0 
              ? (gainEntries.reduce((sum, [, gain]) => sum + gain.productivity_gain_percentage, 0) / gainEntries.length).toFixed(1)
              : 0
            }%
          </span>
        </p>
      </div>
    </div>
  );
};

export default ProductivityGainsTable;
