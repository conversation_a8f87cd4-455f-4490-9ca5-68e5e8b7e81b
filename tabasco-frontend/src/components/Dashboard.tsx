import React, { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useData } from '../contexts/DataContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import ROICharts from './ROICharts';
import ROIConfiguration from './ROIConfiguration';
import {
  LogOut,
  Users,
  DollarSign,
  TrendingUp,
  Calculator,
  Zap,
  Target,
  ArrowUpRight,
  Sparkles,
  BarChart3,
  PieChart,
  Settings,
  Star
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { logout, user } = useAuth();
  const {
    dashboardData,
    roiData,
    isLoading,
    error,
    fetchDashboardData
  } = useData();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleLogout = () => {
    logout();
  };

  // Calculate key metrics
  const metrics = {
    totalEmployees: dashboardData?.random_employees_table?.length || 30,
    monthlySavings: roiData?.reduce((sum, roi) => sum + (roi.total_annual_potential_savings / 12), 0) || 0,
    annualSavings: roiData?.reduce((sum, roi) => sum + roi.total_annual_potential_savings, 0) || 0,
    averageROI: roiData?.length ? roiData.reduce((sum, roi) => sum + roi.roi_percentage, 0) / roiData.length : 0,
    totalCost: roiData?.reduce((sum, roi) => sum + roi.total_annual_cost, 0) || 0,
    netROI: roiData?.reduce((sum, roi) => sum + roi.net_roi, 0) || 0
  };

  // Get top performing tools
  const topTools = roiData?.sort((a, b) => b.roi_percentage - a.roi_percentage).slice(0, 3) || [];

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full border-4 border-muted border-t-primary h-12 w-12" />
          <p className="text-sm text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Card className="w-[400px]">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
            <CardDescription>Something went wrong while loading the dashboard.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Modern Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Tabasco Dashboard
                </h1>
                <p className="text-sm text-gray-500">
                  Welcome back, {user?.username}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={handleLogout}
              className="border-gray-200 hover:bg-gray-50 shadow-sm"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Hero Stats Section */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Your AI Investment Impact
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Track how GenAI tools are transforming your team's productivity and delivering measurable ROI
            </p>
          </div>

          {/* Beautiful Metric Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Team Size Card */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-200 rounded-full -mr-10 -mt-10 opacity-50"></div>
              <CardContent className="p-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <ArrowUpRight className="w-5 h-5 text-green-500" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {metrics.totalEmployees}
                </div>
                <p className="text-sm text-gray-600 font-medium">Team Members</p>
                <div className="mt-3">
                  <Progress value={85} className="h-2" />
                  <p className="text-xs text-gray-500 mt-1">85% adoption rate</p>
                </div>
              </CardContent>
            </Card>

            {/* Monthly Savings Card */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <div className="absolute top-0 right-0 w-20 h-20 bg-green-200 rounded-full -mr-10 -mt-10 opacity-50"></div>
              <CardContent className="p-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                    <DollarSign className="w-6 h-6 text-white" />
                  </div>
                  <ArrowUpRight className="w-5 h-5 text-green-500" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  ${Math.round(metrics.monthlySavings).toLocaleString()}
                </div>
                <p className="text-sm text-gray-600 font-medium">Monthly Savings</p>
                <div className="mt-3 flex items-center">
                  <Badge variant="default" className="bg-green-100 text-green-700 hover:bg-green-100">
                    +24% this month
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* ROI Percentage Card */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-200 rounded-full -mr-10 -mt-10 opacity-50"></div>
              <CardContent className="p-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <ArrowUpRight className="w-5 h-5 text-green-500" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {metrics.averageROI.toFixed(0)}%
                </div>
                <p className="text-sm text-gray-600 font-medium">Average ROI</p>
                <div className="mt-3">
                  <Progress value={Math.min(metrics.averageROI, 100)} className="h-2" />
                  <p className="text-xs text-gray-500 mt-1">
                    {metrics.averageROI > 100 ? 'Exceeding targets' : 'Building momentum'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Net Benefit Card */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-indigo-50 to-indigo-100 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <div className="absolute top-0 right-0 w-20 h-20 bg-indigo-200 rounded-full -mr-10 -mt-10 opacity-50"></div>
              <CardContent className="p-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-indigo-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <ArrowUpRight className="w-5 h-5 text-green-500" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  ${Math.round(metrics.netROI).toLocaleString()}
                </div>
                <p className="text-sm text-gray-600 font-medium">Annual Net Benefit</p>
                <div className="mt-3 flex items-center">
                  <Star className="w-4 h-4 text-yellow-500 mr-1" />
                  <p className="text-xs text-gray-500">Projected for 2024</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Analytics and Tools Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* ROI Charts - Takes 2/3 width */}
          <Card className="lg:col-span-2 bg-white/70 backdrop-blur-sm border-0 shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <BarChart3 className="w-6 h-6 text-blue-600" />
                    ROI Analysis & Trends
                  </CardTitle>
                  <CardDescription className="mt-2">
                    Interactive visualization of your GenAI investment performance
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  Live Data
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <ROICharts data={roiData} />
            </CardContent>
          </Card>

          {/* Top Tools - Takes 1/3 width */}
          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-xl">
                <Zap className="w-6 h-6 text-yellow-600" />
                Top Performers
              </CardTitle>
              <CardDescription>
                Your highest ROI AI tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {topTools.length > 0 ? (
                  topTools.map((tool, index) => (
                    <div key={tool.tool_key} className="relative">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200 hover:shadow-md transition-all">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg ${
                            index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                            index === 1 ? 'bg-gradient-to-r from-gray-400 to-gray-600' :
                            'bg-gradient-to-r from-orange-400 to-orange-600'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-semibold text-gray-900">{tool.tool_name}</p>
                            <p className="text-sm text-gray-600">{tool.num_licenses} licenses</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge
                            variant={tool.roi_percentage > 200 ? "default" : "secondary"}
                            className={tool.roi_percentage > 200 ? "bg-green-100 text-green-800" : ""}
                          >
                            {tool.roi_percentage.toFixed(0)}% ROI
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            ${Math.round(tool.net_roi).toLocaleString()}/year
                          </p>
                        </div>
                      </div>
                      {index === 0 && (
                        <div className="absolute -top-2 -right-2">
                          <Star className="w-5 h-5 text-yellow-500 fill-current" />
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Calculator className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500 font-medium">No data yet</p>
                    <p className="text-sm text-gray-400 mt-1">Configure tools below to see analysis</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Configuration Section */}
        <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-xl">
          <CardHeader className="pb-6">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="w-6 h-6 text-gray-600" />
                  ROI Configuration
                </CardTitle>
                <CardDescription className="mt-2">
                  Customize your analysis by selecting AI tools and adjusting team parameters
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" className="gap-2">
                <PieChart className="w-4 h-4" />
                Export Report
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ROIConfiguration
              tools={dashboardData?.genai_tools_pricing || {}}
              levels={Object.keys(dashboardData?.manual_gains || {})}
            />
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Dashboard;
