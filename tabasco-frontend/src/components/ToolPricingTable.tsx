import React from 'react';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { DollarSign, Star, Info } from 'lucide-react';

interface GenAITool {
  name: string;
  price_per_license_usd_monthly: number;
  features: string[];
  description: string;
}

interface ToolPricingTableProps {
  tools: Record<string, GenAITool>;
}

const ToolPricingTable: React.FC<ToolPricingTableProps> = ({ tools }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getPriceVariant = (price: number) => {
    if (price <= 15) return 'success';
    if (price <= 25) return 'warning';
    return 'error';
  };

  const toolEntries = Object.entries(tools);

  if (toolEntries.length === 0) {
    return (
      <div className="text-center py-8">
        <Info className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
        <p className="text-muted-foreground">No tool pricing data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tool Name</TableHead>
              <TableHead className="text-right">Monthly Price</TableHead>
              <TableHead>Key Features</TableHead>
              <TableHead>Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {toolEntries.map(([toolKey, tool]) => (
              <TableRow key={toolKey}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <div>
                      <div className="font-semibold">{tool.name}</div>
                      <div className="text-xs text-muted-foreground">{toolKey}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <DollarSign className="w-4 h-4 text-muted-foreground" />
                    <Badge variant={getPriceVariant(tool.price_per_license_usd_monthly) as any}>
                      {formatCurrency(tool.price_per_license_usd_monthly)}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1 max-w-xs">
                    {tool.features.slice(0, 3).map((feature, index) => (
                      <div key={index} className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                    {tool.features.length > 3 && (
                      <div className="text-xs text-muted-foreground italic">
                        +{tool.features.length - 3} more features
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-sm">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {tool.description}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Badge variant="success" className="w-3 h-3 p-0"></Badge>
            <span>≤ $15/month</span>
          </div>
          <div className="flex items-center space-x-1">
            <Badge variant="warning" className="w-3 h-3 p-0"></Badge>
            <span>$15-25/month</span>
          </div>
          <div className="flex items-center space-x-1">
            <Badge variant="error" className="w-3 h-3 p-0"></Badge>
            <span>&gt; $25/month</span>
          </div>
        </div>
        <div>
          {toolEntries.length} tool{toolEntries.length !== 1 ? 's' : ''} available
        </div>
      </div>
    </div>
  );
};

export default ToolPricingTable;
