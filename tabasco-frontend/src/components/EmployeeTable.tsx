import React, { useState } from 'react';
import { useData } from '../contexts/DataContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Plus, Trash2, Edit, DollarSign } from 'lucide-react';

interface Employee {
  id: number;
  employee_id: string;
  level: string;
  tenure_in_role: number;
  tenure_since_last_hire: number;
  total_compensation: number;
}

interface EmployeeTableProps {
  employees: Employee[];
  title: string;
  showActions: boolean;
}

const EmployeeTable: React.FC<EmployeeTableProps> = ({ employees, title, showActions }) => {
  const { addEmployee, deleteEmployee } = useData();
  const [isAddingEmployee, setIsAddingEmployee] = useState(false);
  const [newEmployee, setNewEmployee] = useState({
    employee_id: '',
    level: '',
    tenure_in_role: 0,
    tenure_since_last_hire: 0,
    total_compensation: 0,
  });

  const handleAddEmployee = async () => {
    try {
      await addEmployee(newEmployee);
      setNewEmployee({
        employee_id: '',
        level: '',
        tenure_in_role: 0,
        tenure_since_last_hire: 0,
        total_compensation: 0,
      });
      setIsAddingEmployee(false);
    } catch (error) {
      console.error('Failed to add employee:', error);
    }
  };

  const handleDeleteEmployee = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this employee?')) {
      try {
        await deleteEmployee(id);
      } catch (error) {
        console.error('Failed to delete employee:', error);
      }
    }
  };

  const getLevelColor = (level: string) => {
    const levelColors: Record<string, string> = {
      'junior': 'secondary',
      'mid': 'default',
      'senior': 'success',
      'staff': 'warning',
      'principal': 'destructive',
    };
    return levelColors[level.toLowerCase()] || 'default';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-sm text-muted-foreground">
            {employees.length} employee{employees.length !== 1 ? 's' : ''}
          </p>
        </div>
        {showActions && (
          <Button onClick={() => setIsAddingEmployee(true)} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Employee
          </Button>
        )}
      </div>

      {isAddingEmployee && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Add New Employee</CardTitle>
            <CardDescription>
              Enter the details for the new employee
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Employee ID</label>
                <Input
                  placeholder="e.g., EMP001"
                  value={newEmployee.employee_id}
                  onChange={(e) => setNewEmployee({ ...newEmployee, employee_id: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Level</label>
                <Input
                  placeholder="e.g., Senior, Mid, Junior"
                  value={newEmployee.level}
                  onChange={(e) => setNewEmployee({ ...newEmployee, level: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tenure in Role (years)</label>
                <Input
                  type="number"
                  placeholder="0"
                  value={newEmployee.tenure_in_role}
                  onChange={(e) => setNewEmployee({ ...newEmployee, tenure_in_role: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tenure Since Hire (years)</label>
                <Input
                  type="number"
                  placeholder="0"
                  value={newEmployee.tenure_since_last_hire}
                  onChange={(e) => setNewEmployee({ ...newEmployee, tenure_since_last_hire: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium">Total Compensation ($)</label>
                <Input
                  type="number"
                  placeholder="0"
                  value={newEmployee.total_compensation}
                  onChange={(e) => setNewEmployee({ ...newEmployee, total_compensation: Number(e.target.value) })}
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleAddEmployee}>Add Employee</Button>
              <Button variant="outline" onClick={() => setIsAddingEmployee(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee ID</TableHead>
              <TableHead>Level</TableHead>
              <TableHead>Role Tenure</TableHead>
              <TableHead>Company Tenure</TableHead>
              <TableHead>Compensation</TableHead>
              {showActions && <TableHead className="w-[100px]">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {employees.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showActions ? 6 : 5} className="text-center py-8">
                  <div className="text-muted-foreground">
                    <p>No employees found</p>
                    {showActions && (
                      <p className="text-sm mt-1">Click "Add Employee" to get started</p>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              employees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell className="font-medium">
                    {employee.employee_id}
                  </TableCell>
                  <TableCell>
                    <Badge variant={getLevelColor(employee.level) as any}>
                      {employee.level}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {employee.tenure_in_role} year{employee.tenure_in_role !== 1 ? 's' : ''}
                  </TableCell>
                  <TableCell>
                    {employee.tenure_since_last_hire} year{employee.tenure_since_last_hire !== 1 ? 's' : ''}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1 text-muted-foreground" />
                      {formatCurrency(employee.total_compensation)}
                    </div>
                  </TableCell>
                  {showActions && (
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteEmployee(employee.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {employees.length > 0 && (
        <div className="text-sm text-muted-foreground">
          <p>
            Total compensation: {formatCurrency(employees.reduce((sum, emp) => sum + emp.total_compensation, 0))}
          </p>
        </div>
      )}
    </div>
  );
};

export default EmployeeTable;
