import React, { useState } from 'react';
import { useData } from '../contexts/DataContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Switch } from './ui/switch';
import { Checkbox } from './ui/checkbox';
import {
  Calculator,
  Settings,
  Loader2,
  Users,
  Zap,
  DollarSign,
  CheckCircle,
  Sparkles,
  TrendingUp
} from 'lucide-react';

interface GenAITool {
  name: string;
  price_per_license_usd_monthly: number;
  features: string[];
  description: string;
}

interface ROIConfigurationProps {
  tools: Record<string, GenAITool>;
  levels: string[];
}

const ROIConfiguration: React.FC<ROIConfigurationProps> = ({ tools, levels }) => {
  const { calculateROI, isLoading } = useData();
  const [selectedTools, setSelectedTools] = useState<Record<string, { licenses: number }>>({});
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleToolLicenseChange = (toolKey: string, licenses: number) => {
    if (licenses > 0) {
      setSelectedTools(prev => ({
        ...prev,
        [toolKey]: { licenses }
      }));
    } else {
      setSelectedTools(prev => {
        const newTools = { ...prev };
        delete newTools[toolKey];
        return newTools;
      });
    }
  };

  const handleLevelToggle = (level: string) => {
    setSelectedLevels(prev =>
      prev.includes(level)
        ? prev.filter(l => l !== level)
        : [...prev, level]
    );
  };

  const handleCalculateROI = async () => {
    if (Object.keys(selectedTools).length === 0 || selectedLevels.length === 0) {
      alert('Please select at least one tool and one employee level.');
      return;
    }

    setIsCalculating(true);
    try {
      await calculateROI(selectedTools, selectedLevels);
    } catch (error) {
      console.error('Failed to calculate ROI:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const getTotalMonthlyCost = () => {
    return Object.entries(selectedTools).reduce((total, [toolKey, config]) => {
      const tool = tools[toolKey];
      return total + (tool ? tool.price_per_license_usd_monthly * config.licenses : 0);
    }, 0);
  };

  const getTotalLicenses = () => {
    return Object.values(selectedTools).reduce((total, config) => total + config.licenses, 0);
  };

  return (
    <div className="space-y-6">
      {/* Configuration Summary */}
      <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-0 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <span>Configuration Summary</span>
          </CardTitle>
          <CardDescription className="text-base">
            Overview of your current AI tool and team configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-white/70 rounded-xl border border-purple-100">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Zap className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {Object.keys(selectedTools).length}
              </div>
              <div className="text-sm text-gray-600 font-medium">AI Tools Selected</div>
            </div>

            <div className="text-center p-4 bg-white/70 rounded-xl border border-purple-100">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {getTotalLicenses()}
              </div>
              <div className="text-sm text-gray-600 font-medium">Total Licenses</div>
            </div>

            <div className="text-center p-4 bg-white/70 rounded-xl border border-purple-100">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                ${getTotalMonthlyCost().toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 font-medium">Monthly Investment</div>
            </div>
          </div>

          {(Object.keys(selectedTools).length > 0 || selectedLevels.length > 0) && (
            <div className="mt-6 p-4 bg-white/70 rounded-xl border border-purple-100">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.keys(selectedTools).length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                      <Sparkles className="w-4 h-4 text-purple-500" />
                      Selected Tools
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(selectedTools).map(([toolKey, config]) => (
                        <Badge key={toolKey} variant="default" className="bg-purple-100 text-purple-800 border-purple-200">
                          {tools[toolKey]?.name} ({config.licenses})
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {selectedLevels.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                      <Users className="w-4 h-4 text-green-500" />
                      Team Levels
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedLevels.map((level) => (
                        <Badge key={level} variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                          {level.charAt(0).toUpperCase() + level.slice(1)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tool Selection */}
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span>Select AI Tools</span>
            </CardTitle>
            <CardDescription className="text-base">
              Choose your GenAI tools and configure license quantities
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(tools).length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Settings className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 font-medium">No tools available</p>
                <p className="text-sm text-gray-400 mt-1">Tools will appear here when loaded</p>
              </div>
            ) : (
              Object.entries(tools).map(([toolKey, tool]) => (
                <Card key={toolKey} className={`relative overflow-hidden transition-all duration-300 hover:shadow-lg border-2 ${
                  selectedTools[toolKey]
                    ? 'border-blue-200 bg-blue-50/50 shadow-md'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}>
                  {selectedTools[toolKey] && (
                    <div className="absolute top-0 right-0 w-0 h-0 border-l-[40px] border-l-transparent border-t-[40px] border-t-blue-500">
                      <CheckCircle className="absolute -top-8 -right-8 w-4 h-4 text-white" />
                    </div>
                  )}

                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="text-lg font-semibold text-gray-900">{tool.name}</h4>
                          <Badge variant="outline" className="bg-white">
                            ${tool.price_per_license_usd_monthly}/month
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                          {tool.description}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={!!selectedTools[toolKey]}
                            onCheckedChange={(checked: boolean) => {
                              if (checked) {
                                handleToolLicenseChange(toolKey, 1);
                              } else {
                                handleToolLicenseChange(toolKey, 0);
                              }
                            }}
                          />
                          <span className="text-sm font-medium text-gray-700">
                            {selectedTools[toolKey] ? 'Selected' : 'Select'}
                          </span>
                        </div>

                        {selectedTools[toolKey] && (
                          <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-gray-700">Licenses:</label>
                            <Input
                              type="number"
                              min="1"
                              max="1000"
                              value={selectedTools[toolKey]?.licenses || 1}
                              onChange={(e) => handleToolLicenseChange(toolKey, parseInt(e.target.value) || 1)}
                              className="w-20 h-8 text-center"
                            />
                          </div>
                        )}
                      </div>

                      {selectedTools[toolKey] && (
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">
                            ${(tool.price_per_license_usd_monthly * (selectedTools[toolKey]?.licenses || 0)).toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">per month</div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-wrap gap-2 mt-4">
                      {tool.features.slice(0, 4).map((feature, index) => (
                        <Badge key={index} variant="secondary" className="text-xs bg-gray-100 text-gray-700">
                          {feature}
                        </Badge>
                      ))}
                      {tool.features.length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{tool.features.length - 4} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </CardContent>
        </Card>

        {/* Employee Level Selection */}
        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <Users className="w-5 h-5 text-white" />
              </div>
              <span>Select Team Levels</span>
            </CardTitle>
            <CardDescription className="text-base">
              Choose which employee levels will use these AI tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {levels.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 font-medium">No employee levels available</p>
                <p className="text-sm text-gray-400 mt-1">Levels will appear here when loaded</p>
              </div>
            ) : (
              levels.map((level) => (
                <Card key={level} className={`relative overflow-hidden transition-all duration-300 hover:shadow-md border-2 cursor-pointer ${
                  selectedLevels.includes(level)
                    ? 'border-green-200 bg-green-50/50 shadow-sm'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`} onClick={() => handleLevelToggle(level)}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={selectedLevels.includes(level)}
                          onCheckedChange={() => handleLevelToggle(level)}
                          className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                        />
                        <div>
                          <h4 className="font-semibold capitalize text-gray-900">{level}</h4>
                          <p className="text-sm text-gray-600">
                            {level.charAt(0).toUpperCase() + level.slice(1)} level employees
                          </p>
                        </div>
                      </div>

                      {selectedLevels.includes(level) && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                            Selected
                          </Badge>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Calculate ROI Button */}
      <div className="flex justify-center pt-6">
        <Button
          onClick={handleCalculateROI}
          disabled={Object.keys(selectedTools).length === 0 || selectedLevels.length === 0 || isCalculating || isLoading}
          size="lg"
          className="px-12 py-4 text-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        >
          {isCalculating || isLoading ? (
            <>
              <Loader2 className="w-5 h-5 mr-3 animate-spin" />
              Calculating Your ROI...
            </>
          ) : (
            <>
              <Calculator className="w-5 h-5 mr-3" />
              Calculate ROI Analysis
            </>
          )}
        </Button>
      </div>

      {/* Quick Guide */}
      {Object.keys(selectedTools).length === 0 && selectedLevels.length === 0 && (
        <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Ready to Calculate ROI?</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Select your AI tools and team levels above, then click the calculate button to see your potential ROI.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xs">1</span>
                </div>
                <span>Choose AI tools & licenses</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold text-xs">2</span>
                </div>
                <span>Select employee levels</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ROIConfiguration;
