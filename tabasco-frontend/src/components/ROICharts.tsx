import React, { useState } from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { BarChart3, LineChart as LineChartIcon, <PERSON><PERSON><PERSON> as PieChartIcon, TrendingUp } from 'lucide-react';

interface ROIData {
  tool_name: string;
  tool_key: string;
  total_annual_cost: number;
  total_annual_potential_savings: number;
  net_roi: number;
  roi_percentage: number;
  num_licenses: number;
  affected_employees: number;
}

interface ROIChartsProps {
  data: ROIData[];
}

const ROICharts: React.FC<ROIChartsProps> = ({ data }) => {
  const [activeChart, setActiveChart] = useState<'bar' | 'line' | 'pie' | 'area'>('bar');

  const chartTypes = [
    { id: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { id: 'line', label: 'Line Chart', icon: LineChartIcon },
    { id: 'pie', label: 'Pie Chart', icon: PieChartIcon },
    { id: 'area', label: 'Area Chart', icon: TrendingUp },
  ];

  // Prepare data for charts
  const chartData = data.map((item) => ({
    name: item.tool_name,
    cost: item.total_annual_cost,
    savings: item.total_annual_potential_savings,
    netROI: item.net_roi,
    roiPercentage: item.roi_percentage,
    licenses: item.num_licenses,
    employees: item.affected_employees,
  }));

  // Colors for charts
  const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-semibold">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' 
                ? entry.name.includes('Percentage') 
                  ? `${entry.value.toFixed(1)}%`
                  : `$${entry.value.toLocaleString()}`
                : entry.value
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-12">
        <TrendingUp className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No ROI Data Available</h3>
        <p className="text-muted-foreground mb-4">
          Configure your tools and calculate ROI to see interactive charts here.
        </p>
        <Badge variant="outline">Configure ROI in the Configuration tab</Badge>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Chart Type Selector */}
      <div className="flex flex-wrap gap-2">
        {chartTypes.map((type) => {
          const Icon = type.icon;
          return (
            <Button
              key={type.id}
              variant={activeChart === type.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveChart(type.id as any)}
            >
              <Icon className="w-4 h-4 mr-2" />
              {type.label}
            </Button>
          );
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Main Chart */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>
              {activeChart === 'bar' && 'ROI Comparison'}
              {activeChart === 'line' && 'ROI Trend Analysis'}
              {activeChart === 'pie' && 'Cost Distribution'}
              {activeChart === 'area' && 'Savings vs Cost Analysis'}
            </CardTitle>
            <CardDescription>
              {activeChart === 'bar' && 'Compare costs, savings, and ROI across different tools'}
              {activeChart === 'line' && 'Track ROI percentage trends across tools'}
              {activeChart === 'pie' && 'Visualize cost distribution by tool'}
              {activeChart === 'area' && 'Area chart showing the relationship between costs and savings'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                {activeChart === 'bar' ? (
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="cost" fill="#ef4444" name="Annual Cost" />
                    <Bar dataKey="savings" fill="#10b981" name="Annual Savings" />
                  </BarChart>
                ) : activeChart === 'line' ? (
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="roiPercentage" 
                      stroke="#3b82f6" 
                      strokeWidth={3}
                      name="ROI Percentage"
                    />
                  </LineChart>
                ) : activeChart === 'pie' ? (
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: $${value.toLocaleString()}`}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="cost"
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                ) : (
                  <AreaChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="cost" 
                      stackId="1" 
                      stroke="#ef4444" 
                      fill="#ef4444" 
                      fillOpacity={0.6}
                      name="Annual Cost"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="savings" 
                      stackId="2" 
                      stroke="#10b981" 
                      fill="#10b981" 
                      fillOpacity={0.6}
                      name="Annual Savings"
                    />
                  </AreaChart>
                )}
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* ROI Summary */}
        <Card>
          <CardHeader>
            <CardTitle>ROI Summary</CardTitle>
            <CardDescription>Key metrics from your ROI analysis</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Annual Cost:</span>
                <span className="font-semibold">
                  ${chartData.reduce((sum, item) => sum + item.cost, 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Annual Savings:</span>
                <span className="font-semibold text-green-600">
                  ${chartData.reduce((sum, item) => sum + item.savings, 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Net ROI:</span>
                <span className="font-semibold">
                  ${chartData.reduce((sum, item) => sum + item.netROI, 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Average ROI:</span>
                <Badge variant={
                  chartData.reduce((sum, item) => sum + item.roiPercentage, 0) / chartData.length > 100 
                    ? 'success' 
                    : 'warning'
                }>
                  {(chartData.reduce((sum, item) => sum + item.roiPercentage, 0) / chartData.length).toFixed(1)}%
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tool Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Tool Performance</CardTitle>
            <CardDescription>Individual tool ROI rankings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {chartData
                .sort((a, b) => b.roiPercentage - a.roiPercentage)
                .map((tool, index) => (
                  <div key={tool.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors[index % colors.length] }}></div>
                      <span className="text-sm font-medium">{tool.name}</span>
                    </div>
                    <Badge variant={tool.roiPercentage > 100 ? 'success' : 'warning'}>
                      {tool.roiPercentage.toFixed(1)}%
                    </Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ROICharts;
