import React, { createContext, useContext, useState, ReactNode } from 'react';
import axios from 'axios';

interface Employee {
  id: number;
  employee_id: string;
  level: string;
  tenure_in_role: number;
  tenure_since_last_hire: number;
  total_compensation: number;
}

interface GenAITool {
  name: string;
  price_per_license_usd_monthly: number;
  features: string[];
  description: string;
}

interface ProductivityGain {
  role: string;
  productivity_gain_percentage: number;
  notes: string;
}

interface ROIData {
  tool_name: string;
  tool_key: string;
  total_annual_cost: number;
  total_annual_potential_savings: number;
  net_roi: number;
  roi_percentage: number;
  num_licenses: number;
  affected_employees: number;
}

interface DashboardData {
  user_employees: Employee[];
  random_employees_table: Employee[];
  genai_tools_pricing: Record<string, GenAITool>;
  manual_gains: Record<string, ProductivityGain>;
  user: {
    id: number;
    username: string;
    email: string;
  };
}

interface DataContextType {
  dashboardData: DashboardData | null;
  roiData: ROIData[];
  isLoading: boolean;
  error: string | null;
  fetchDashboardData: () => Promise<void>;
  calculateROI: (selectedTools: Record<string, { licenses: number }>, selectedLevels: string[]) => Promise<void>;
  addEmployee: (employee: Omit<Employee, 'id'>) => Promise<void>;
  deleteEmployee: (id: number) => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [roiData, setRoiData] = useState<ROIData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get('/api/dashboard-data');
      setDashboardData(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateROI = async (
    selectedTools: Record<string, { licenses: number }>,
    selectedLevels: string[]
  ) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.post('/api/calculate-roi', {
        selected_tools: selectedTools,
        selected_levels: selectedLevels,
      });
      setRoiData(response.data.savings_data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to calculate ROI');
    } finally {
      setIsLoading(false);
    }
  };

  const addEmployee = async (employee: Omit<Employee, 'id'>) => {
    try {
      const response = await axios.post('/api/employees', employee);
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          user_employees: [...dashboardData.user_employees, response.data],
        });
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add employee');
      throw err;
    }
  };

  const deleteEmployee = async (id: number) => {
    try {
      await axios.delete(`/api/employees/${id}`);
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          user_employees: dashboardData.user_employees.filter(emp => emp.id !== id),
        });
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete employee');
      throw err;
    }
  };

  const value = {
    dashboardData,
    roiData,
    isLoading,
    error,
    fetchDashboardData,
    calculateROI,
    addEmployee,
    deleteEmployee,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
