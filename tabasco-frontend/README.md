# 🌶️ Tabasco Frontend

Modern React frontend for the Tabasco GenAI ROI Calculator with beautiful shadcn/ui components.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation

1. **Install dependencies:**
```bash
npm install
```

2. **Start development server:**
```bash
npm start
```

The app will be available at `http://localhost:3000`

## 🎨 Features

### Modern UI Components
- **shadcn/ui** - Beautiful, accessible components
- **Tailwind CSS** - Utility-first styling
- **Lucide React** - Modern icon library
- **Radix UI** - Unstyled, accessible primitives

### Dashboard Features
- **Gradient backgrounds** with glassmorphism effects
- **Interactive tool selection** with switches and checkboxes
- **Real-time cost calculations** and ROI analysis
- **Responsive design** that works on all devices
- **Professional animations** and hover effects

### Authentication
- **Secure login/logout** functionality
- **Protected routes** with automatic redirects
- **User session management**

## 🔧 Configuration

The frontend is configured to communicate with the Tabasco backend at:
- `http://localhost:5004` (default)

To change the backend URL, update `src/contexts/AuthContext.tsx`:
```typescript
axios.defaults.baseURL = 'http://your-backend-url';
```

## 🛠️ Development

### Available Scripts
- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

### Project Structure
```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── Dashboard.tsx   # Main dashboard
│   └── Login.tsx       # Login page
├── contexts/           # React contexts
└── lib/               # Utility functions
```

## 🎯 Login Credentials

Default login:
- **Email:** `<EMAIL>`
- **Password:** `password`
