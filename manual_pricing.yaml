# Manual pricing data for services that are difficult to scrape automatically
# This file should be updated manually when pricing changes are detected
# Last updated: 2025-05-24

manual_pricing_data:
  cursor:
    name: "Cursor"
    url: "https://cursor.sh/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 20.0
        currency: "USD"
        billing_period: "month"
        notes: "Pro plan with advanced AI features"
      business:
        price: 40.0
        currency: "USD"
        billing_period: "month"
        notes: "Business plan with team features"
    
  codeium:
    name: "Codeium"
    url: "https://codeium.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      individual:
        price: 12.0
        currency: "USD"
        billing_period: "month"
        notes: "Individual developer plan"
      teams:
        price: 35.0
        currency: "USD"
        billing_period: "month"
        notes: "Team collaboration features"
    
  tabnine:
    name: "Tabnine"
    url: "https://www.tabnine.com/pricing"
    last_manual_update: "2025-05-24"
    pricing_plans:
      pro:
        price: 12.0
        currency: "USD"
        billing_period: "month"
        notes: "Pro plan with advanced completions"
      enterprise:
        price: 39.0
        currency: "USD"
        billing_period: "month"
        notes: "Enterprise plan with custom models"

# Instructions for updating this file:
# 1. Visit the service's pricing page
# 2. Update the pricing_plans section with current prices
# 3. Update the last_manual_update date
# 4. Add notes about plan features if helpful
# 5. Run the scraper to regenerate output files
