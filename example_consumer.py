"""Example script showing how to consume the pricing data from other components."""

import yaml
import json
from publisher import PricingPublisher


def read_yaml_data():
    """Example of reading YAML pricing data."""
    print("=== Reading YAML Data ===")
    
    try:
        with open('output/genai_pricing.yaml', 'r') as f:
            data = yaml.safe_load(f)
        
        services = data['genai_pricing_data']['services']
        
        for service_key, service_info in services.items():
            print(f"\nService: {service_info['name']}")
            print(f"Status: {service_info['status']}")
            
            if service_info['status'] == 'success':
                pricing_plans = service_info['pricing_plans']
                for plan_name, plan_details in pricing_plans.items():
                    price = plan_details['price']
                    currency = plan_details['currency']
                    period = plan_details['billing_period']
                    print(f"  {plan_name}: {currency} {price}/{period}")
    
    except FileNotFoundError:
        print("No YAML data file found. Run the scraper first.")
    except Exception as e:
        print(f"Error reading YAML data: {e}")


def read_json_data():
    """Example of reading JSON pricing data."""
    print("\n=== Reading JSON Data ===")
    
    try:
        with open('output/genai_pricing.json', 'r') as f:
            data = json.load(f)
        
        services = data['services']
        
        for service_key, service_info in services.items():
            print(f"\nService: {service_info['name']}")
            print(f"URL: {service_info['url']}")
            
            if service_info['status'] == 'success':
                pricing = service_info['pricing']
                for plan_name, plan_details in pricing.items():
                    price = plan_details['price']
                    currency = plan_details['currency']
                    period = plan_details['period']
                    print(f"  {plan_name}: {currency} {price}/{period}")
    
    except FileNotFoundError:
        print("No JSON data file found. Run the scraper first.")
    except Exception as e:
        print(f"Error reading JSON data: {e}")


def read_api_data():
    """Example of reading API-formatted data."""
    print("\n=== Reading API Data ===")
    
    try:
        with open('output/api_pricing.json', 'r') as f:
            data = json.load(f)
        
        print(f"API Version: {data['api_version']}")
        print(f"Data Timestamp: {data['timestamp']}")
        print(f"Services Count: {len(data['services'])}")
        
        for service in data['services']:
            print(f"\nService ID: {service['id']}")
            print(f"Name: {service['name']}")
            print(f"Provider URL: {service['provider_url']}")
            
            for tier in service['pricing_tiers']:
                tier_name = tier['tier_name']
                price = tier['price']
                currency = tier['currency']
                cycle = tier['billing_cycle']
                print(f"  {tier_name}: {currency} {price}/{cycle}")
    
    except FileNotFoundError:
        print("No API data file found. Run the scraper first.")
    except Exception as e:
        print(f"Error reading API data: {e}")


def use_publisher_class():
    """Example of using the PricingPublisher class directly."""
    print("\n=== Using Publisher Class ===")
    
    try:
        publisher = PricingPublisher()
        
        # Read latest YAML data
        yaml_data = publisher.get_latest_data('yaml')
        if yaml_data:
            services_count = len(yaml_data.get('genai_pricing_data', {}).get('services', {}))
            print(f"Found {services_count} services in YAML data")
        
        # Read latest JSON data
        json_data = publisher.get_latest_data('json')
        if json_data:
            metadata = json_data.get('metadata', {})
            successful_scrapes = metadata.get('successful_scrapes', 0)
            total_services = metadata.get('total_services', 0)
            print(f"Success rate: {successful_scrapes}/{total_services}")
    
    except Exception as e:
        print(f"Error using publisher class: {e}")


def find_cheapest_service():
    """Example of analyzing pricing data to find the cheapest service."""
    print("\n=== Finding Cheapest Service ===")
    
    try:
        with open('output/api_pricing.json', 'r') as f:
            data = json.load(f)
        
        cheapest_service = None
        cheapest_price = float('inf')
        
        for service in data['services']:
            for tier in service['pricing_tiers']:
                if tier['price'] < cheapest_price:
                    cheapest_price = tier['price']
                    cheapest_service = {
                        'name': service['name'],
                        'tier': tier['tier_name'],
                        'price': tier['price'],
                        'currency': tier['currency'],
                        'cycle': tier['billing_cycle']
                    }
        
        if cheapest_service:
            print(f"Cheapest option: {cheapest_service['name']} ({cheapest_service['tier']})")
            print(f"Price: {cheapest_service['currency']} {cheapest_service['price']}/{cheapest_service['cycle']}")
        else:
            print("No pricing data available")
    
    except FileNotFoundError:
        print("No API data file found. Run the scraper first.")
    except Exception as e:
        print(f"Error analyzing pricing data: {e}")


if __name__ == "__main__":
    print("GenAI Pricing Data Consumer Examples")
    print("=" * 50)
    
    # Demonstrate different ways to read the data
    read_yaml_data()
    read_json_data()
    read_api_data()
    use_publisher_class()
    find_cheapest_service()
    
    print("\n" + "=" * 50)
    print("Integration examples completed!")
