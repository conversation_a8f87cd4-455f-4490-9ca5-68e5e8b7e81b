name: tabasco-genai-roi-calculator
region: nyc1

services:
  # Backend Flask API
  - name: tabasco-backend
    source_dir: /tabasco-backend
    github:
      repo: mbparsa/Tabasco
      branch: feature/real-usage-analytics
      deploy_on_push: true
    run_command: python app.py
    environment_slug: python
    instance_count: 1
    instance_size_slug: basic-xxs
    http_port: 5004
    health_check:
      http_path: /api/user
    envs:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: "0"
      - key: DATABASE_URL
        value: sqlite:///tabasco.db
      - key: SECRET_KEY
        value: your-secret-key-here-change-in-production
      - key: CORS_ORIGINS
        value: ${APP_DOMAIN}
    routes:
      - path: /api

  # Frontend Next.js Application  
  - name: tabasco-frontend
    source_dir: /tabasco-new-ui
    github:
      repo: mbparsa/Tabasco
      branch: feature/real-usage-analytics
      deploy_on_push: true
    build_command: npm run build
    run_command: npm start
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    http_port: 3000
    health_check:
      http_path: /
    envs:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_API_BASE_URL
        value: ${tabasco-backend.PUBLIC_URL}
    routes:
      - path: /

# Database (using managed database for production)
databases:
  - name: tabasco-db
    engine: PG
    version: "14"
    size: db-s-dev-database

# Static assets (optional)
static_sites: []

# Domain configuration
domains:
  - domain: tabasco-app.com
    type: PRIMARY
    wildcard: false
    certificate_id: ""

# Alerts
alerts:
  - rule: CPU_UTILIZATION
    disabled: false
  - rule: MEM_UTILIZATION  
    disabled: false
  - rule: RESTART_COUNT
    disabled: false

# Features
features:
  - buildpack-stack=ubuntu-22
