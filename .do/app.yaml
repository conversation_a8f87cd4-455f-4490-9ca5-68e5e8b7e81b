name: tabasco-backend-api

services:
  # Backend Flask API Only
  - name: tabasco-backend
    source_dir: /tabasco-backend
    github:
      repo: mbparsa/Tabasco
      branch: feature/real-usage-analytics
      deploy_on_push: true
    environment_slug: python
    instance_count: 1
    instance_size_slug: basic-xxs
    http_port: 8080
    health_check:
      http_path: /api/health
    envs:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: "0"
      - key: PORT
        value: "8080"
      - key: HOST
        value: "0.0.0.0"
      - key: SECRET_KEY
        value: tabasco-secret-key-change-in-production-2024
      - key: CORS_ORIGINS
        value: "https://tabasco-teal.vercel.app,https://*.vercel.app,*"

# Optional: Add managed database later
# databases:
#   - name: tabasco-db
#     engine: PG
#     version: "14"
#     size: db-s-dev-database
