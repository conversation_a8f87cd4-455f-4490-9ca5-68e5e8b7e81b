name: tabasco-genai-roi-calculator

services:
  # Backend Flask API
  - name: tabasco-backend
    source_dir: /tabasco-backend
    github:
      repo: mbparsa/Tabasco
      branch: feature/real-usage-analytics
      deploy_on_push: true
    environment_slug: python
    instance_count: 1
    instance_size_slug: basic-xxs
    http_port: 8080
    health_check:
      http_path: /api/health
    envs:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: "0"
      - key: SECRET_KEY
        value: tabasco-secret-key-change-in-production-2024
      - key: CORS_ORIGINS
        value: ${APP_DOMAIN}
    routes:
      - path: /api

  # Frontend Next.js Application
  - name: tabasco-frontend
    source_dir: /tabasco-new-ui
    github:
      repo: mbparsa/Tabasco
      branch: feature/real-usage-analytics
      deploy_on_push: true
    build_command: npm ci && npm run build
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    http_port: 8080
    health_check:
      http_path: /
    envs:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_API_BASE_URL
        value: ${tabasco-backend.PUBLIC_URL}
    routes:
      - path: /

# Optional: Add managed database later
# databases:
#   - name: tabasco-db
#     engine: PG
#     version: "14"
#     size: db-s-dev-database
