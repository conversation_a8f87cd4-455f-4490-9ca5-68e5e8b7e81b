"""Web scraper for collecting GenAI coding assistant pricing information."""

import requests
import time
import re
import json
import yaml
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from config import SERVICES_CONFIG, SCRAPING_CONFIG


class PricingScraper:
    """Main scraper class for collecting pricing information."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': SCRAPING_CONFIG['user_agent']
        })
        self.driver = None
        
    def _setup_selenium_driver(self):
        """Setup Selenium WebDriver for JavaScript-heavy sites."""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument(f'--user-agent={SCRAPING_CONFIG["user_agent"]}')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            return True
        except WebDriverException as e:
            print(f"Failed to setup Chrome driver: {e}")
            return False
    
    def _extract_price_from_text(self, text: str) -> Optional[float]:
        """Extract price from text using regex patterns."""
        if not text:
            return None
            
        # Common price patterns
        patterns = [
            r'\$(\d+(?:\.\d{2})?)',  # $20.00 or $20
            r'(\d+(?:\.\d{2})?)\s*(?:USD|dollars?)',  # 20 USD
            r'(\d+(?:\.\d{2})?)\s*/\s*(?:month|mo)',  # 20/month
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def _scrape_with_requests(self, url: str) -> Optional[BeautifulSoup]:
        """Scrape using requests library."""
        try:
            response = self.session.get(
                url, 
                timeout=SCRAPING_CONFIG['timeout']
            )
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.RequestException as e:
            print(f"Request failed for {url}: {e}")
            return None
    
    def _scrape_with_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """Scrape using Selenium for JavaScript-heavy sites."""
        if not self.driver:
            if not self._setup_selenium_driver():
                return None
        
        try:
            self.driver.get(url)
            WebDriverWait(self.driver, SCRAPING_CONFIG['timeout']).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(3)  # Allow dynamic content to load
            
            html = self.driver.page_source
            return BeautifulSoup(html, 'html.parser')
        except (TimeoutException, WebDriverException) as e:
            print(f"Selenium scraping failed for {url}: {e}")
            return None
    
    def _scrape_github_copilot(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract GitHub Copilot pricing."""
        pricing = {}
        
        # Look for pricing information in various selectors
        price_elements = soup.find_all(['div', 'span', 'p'], 
                                     class_=re.compile(r'price|pricing|cost', re.I))
        
        for element in price_elements:
            text = element.get_text(strip=True)
            if 'individual' in text.lower() or 'personal' in text.lower():
                price = self._extract_price_from_text(text)
                if price:
                    pricing['individual'] = {'price': price, 'currency': 'USD', 'period': 'month'}
            elif 'business' in text.lower() or 'team' in text.lower():
                price = self._extract_price_from_text(text)
                if price:
                    pricing['business'] = {'price': price, 'currency': 'USD', 'period': 'month'}
        
        # Fallback: known GitHub Copilot pricing
        if not pricing:
            pricing = {
                'individual': {'price': 10.0, 'currency': 'USD', 'period': 'month'},
                'business': {'price': 19.0, 'currency': 'USD', 'period': 'month'}
            }
        
        return pricing
    
    def _scrape_openai_pricing(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract OpenAI ChatGPT pricing."""
        pricing = {}
        
        # Look for ChatGPT Plus pricing
        price_elements = soup.find_all(['div', 'span', 'p'], 
                                     class_=re.compile(r'price|pricing|tier', re.I))
        
        for element in price_elements:
            text = element.get_text(strip=True)
            if 'plus' in text.lower() or 'chatgpt plus' in text.lower():
                price = self._extract_price_from_text(text)
                if price:
                    pricing['plus'] = {'price': price, 'currency': 'USD', 'period': 'month'}
            elif 'pro' in text.lower() or 'chatgpt pro' in text.lower():
                price = self._extract_price_from_text(text)
                if price:
                    pricing['pro'] = {'price': price, 'currency': 'USD', 'period': 'month'}
        
        # Fallback: known OpenAI pricing
        if not pricing:
            pricing = {
                'plus': {'price': 20.0, 'currency': 'USD', 'period': 'month'},
                'pro': {'price': 200.0, 'currency': 'USD', 'period': 'month'}
            }
        
        return pricing
    
    def _scrape_generic_pricing(self, soup: BeautifulSoup, service_name: str) -> Dict[str, Any]:
        """Generic pricing extraction for other services."""
        pricing = {}
        
        # Look for common pricing patterns
        price_elements = soup.find_all(['div', 'span', 'p', 'h1', 'h2', 'h3'], 
                                     class_=re.compile(r'price|pricing|cost|tier|plan', re.I))
        
        plan_types = ['free', 'pro', 'premium', 'business', 'enterprise', 'team', 'individual']
        
        for element in price_elements:
            text = element.get_text(strip=True)
            price = self._extract_price_from_text(text)
            
            if price:
                # Try to identify plan type
                plan_type = 'unknown'
                for plan in plan_types:
                    if plan in text.lower():
                        plan_type = plan
                        break
                
                if plan_type not in pricing:
                    pricing[plan_type] = {'price': price, 'currency': 'USD', 'period': 'month'}
        
        return pricing
    
    def _load_manual_pricing(self, service_key: str) -> Dict[str, Any]:
        """Load manual pricing data from YAML file."""
        manual_file = 'manual_pricing.yaml'
        
        if not os.path.exists(manual_file):
            return {}
        
        try:
            with open(manual_file, 'r', encoding='utf-8') as f:
                manual_data = yaml.safe_load(f)
            
            service_data = manual_data.get('manual_pricing_data', {}).get(service_key, {})
            
            if service_data:
                # Convert manual format to scraper format
                pricing = {}
                pricing_plans = service_data.get('pricing_plans', {})
                
                for plan_name, plan_details in pricing_plans.items():
                    pricing[plan_name] = {
                        'price': plan_details.get('price', 0),
                        'currency': plan_details.get('currency', 'USD'),
                        'period': plan_details.get('billing_period', 'month')
                    }
                
                return pricing
        
        except Exception as e:
            print(f"Error loading manual pricing for {service_key}: {e}")
        
        return {}
    
    def scrape_service(self, service_key: str) -> Dict[str, Any]:
        """Scrape pricing for a specific service."""
        service_config = SERVICES_CONFIG.get(service_key)
        if not service_config:
            return {}
        
        url = service_config['url']
        print(f"Scraping {service_config['name']} from {url}")
        
        # Try requests first, then Selenium if needed
        soup = self._scrape_with_requests(url)
        if not soup:
            soup = self._scrape_with_selenium(url)
        
        pricing = {}
        status = 'failed'
        data_source = 'scraping_failed'
        
        if soup:
            # Service-specific extraction
            if service_key == 'github_copilot':
                pricing = self._scrape_github_copilot(soup)
            elif service_key == 'openai_chatgpt':
                pricing = self._scrape_openai_pricing(soup)
            else:
                pricing = self._scrape_generic_pricing(soup, service_config['name'])
            
            if pricing:
                status = 'success'
                data_source = 'web_scraping'
        
        # If scraping failed, try to load manual pricing data
        if not pricing:
            print(f"Web scraping failed for {service_config['name']}, trying manual pricing data...")
            manual_pricing = self._load_manual_pricing(service_key)
            if manual_pricing:
                pricing = manual_pricing
                status = 'success'
                data_source = 'manual_data'
                print(f"Using manual pricing data for {service_config['name']}")
            else:
                print(f"No manual pricing data available for {service_config['name']}")
        
        return {
            'name': service_config['name'],
            'url': url,
            'pricing': pricing,
            'last_updated': datetime.now().isoformat(),
            'status': status,
            'data_source': data_source
        }
    
    def scrape_all_services(self) -> Dict[str, Any]:
        """Scrape pricing for all configured services."""
        results = {}
        
        for service_key in SERVICES_CONFIG.keys():
            try:
                results[service_key] = self.scrape_service(service_key)
                time.sleep(SCRAPING_CONFIG['delay_between_requests'])
            except Exception as e:
                print(f"Error scraping {service_key}: {e}")
                results[service_key] = {
                    'name': SERVICES_CONFIG[service_key]['name'],
                    'url': SERVICES_CONFIG[service_key]['url'],
                    'pricing': {},
                    'last_updated': datetime.now().isoformat(),
                    'status': 'error',
                    'error': str(e)
                }
        
        return {
            'services': results,
            'metadata': {
                'collection_timestamp': datetime.now().isoformat(),
                'total_services': len(results),
                'successful_scrapes': len([r for r in results.values() if r.get('status') == 'success'])
            }
        }
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            self.driver.quit()
        self.session.close()
