# 🚀 Tabasco Quick Start Guide

Get the Tabasco application up and running in seconds with our automated startup scripts!

## ⚡ One-Command Start

```bash
./start-tabasco.sh
```

That's it! This single command will:
- ✅ Set up all necessary directories
- ✅ Install all dependencies (Python & Node.js)
- ✅ Create environment files from examples
- ✅ Set up the database and demo user
- ✅ Start both backend and frontend services
- ✅ Handle port conflicts automatically

## 🎯 What You Get

After running the startup script, you'll have:

- **🌶️ Backend API**: http://localhost:5004
- **🎨 Frontend UI**: http://localhost:3000 (or next available port)
- **👤 Demo User**:
  - Username: `demo`
  - Password: `demo123`
- **🔐 Admin User**:
  - Username: `admin`
  - Password: `admin123`

## 📋 Prerequisites

Make sure you have these installed:
- **Python 3.7+** with pip
- **Node.js 16+** with npm
- **Git** (for cloning the repository)

## 🛠️ Advanced Options

### Start Individual Services

```bash
# Backend only
./start-tabasco.sh --backend-only

# Frontend only
./start-tabasco.sh --frontend-only
```

### Manual Service Control

```bash
# Backend
cd tabasco-backend && ./start-backend.sh

# Frontend
cd tabasco-new-ui && ./start-frontend.sh
```

### Windows Users

```batch
REM Backend
cd tabasco-backend
start-backend.bat

REM Frontend
cd tabasco-new-ui
start-frontend.bat
```

## 🔧 Script Features

### Backend Script (`start-backend.sh`)
- Creates necessary directories (`instance`, `logs`)
- Installs Python dependencies
- Sets up environment configuration
- Creates demo user automatically
- Handles port conflicts
- Provides colored status output

### Frontend Script (`start-frontend.sh`)
- Installs Node.js dependencies with conflict resolution
- Sets up environment configuration
- Finds available ports automatically (3000, 3001, 3002...)
- Provides helpful status information

### Master Script (`start-tabasco.sh`)
- Orchestrates both services
- Supports command-line options
- Handles graceful shutdown
- Provides comprehensive status updates

## 🚨 Troubleshooting

### Port Already in Use
The scripts automatically find available ports. If you see warnings about ports being in use, the scripts will:
- Try alternative ports (3001, 3002, etc.)
- Kill existing processes if needed
- Update CORS configuration accordingly

### Permission Denied
Make scripts executable:
```bash
chmod +x start-tabasco.sh
chmod +x tabasco-backend/start-backend.sh
chmod +x tabasco-new-ui/start-frontend.sh
```

### Dependencies Issues
The scripts handle most dependency issues automatically, but if you encounter problems:
```bash
# Backend dependencies
cd tabasco-backend && pip install -r requirements.txt

# Frontend dependencies
cd tabasco-new-ui && npm install --legacy-peer-deps
```

## 🎉 Success Indicators

Look for these messages to confirm everything is working:

```
🚀 Backend starting on http://localhost:5004
🚀 Frontend starting on http://localhost:3000
📊 Default login credentials:
   👤 Demo User: demo / demo123
   🔐 Admin User: admin / admin123
```

## 🔄 Stopping Services

Press `Ctrl+C` in the terminal where you ran the startup script. The master script will gracefully shut down both services.

## 📚 Next Steps

1. **Open your browser** to http://localhost:3000
2. **Login** with either:
   - Demo User: `demo` / `demo123`
   - Admin User: `admin` / `admin123`
3. **Explore** the Tabasco ROI dashboard
4. **Configure** your environment variables as needed
5. **Read** CONFIGURATION.md for advanced setup options

## 🆘 Need Help?

- Check `CONFIGURATION.md` for detailed configuration options
- Review individual service README files
- Check the console output for error messages
- Ensure all prerequisites are installed

Happy Tabasco-ing! 🌶️
