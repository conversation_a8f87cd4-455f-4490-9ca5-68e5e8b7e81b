const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5004'

// Generic API call function with authentication
async function apiCall(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE}${endpoint}`

  const defaultOptions: RequestInit = {
    credentials: 'include', // Include cookies for session
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, defaultOptions)

    // Handle authentication redirects
    if (response.status === 401 || response.redirected) {
      throw new Error('Authentication required')
    }

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`)
    }

    // Check if response has content before trying to parse JSON
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text()
      console.warn(`Non-JSON response from ${endpoint}:`, text)
      throw new Error(`Expected JSON response but got: ${contentType}`)
    }

    const text = await response.text()
    if (!text.trim()) {
      console.warn(`Empty response from ${endpoint}`)
      return {}
    }

    try {
      return JSON.parse(text)
    } catch (parseError) {
      console.error(`JSON parse error for ${endpoint}:`, parseError)
      console.error(`Response text:`, text)
      throw new Error(`Invalid JSON response from ${endpoint}`)
    }
  } catch (error) {
    console.error(`API call to ${endpoint} failed:`, error)
    throw error
  }
}

// Dashboard API calls
export const dashboardAPI = {
  getMetrics: () => apiCall('/api/dashboard/metrics'),
  getUsageAnalytics: () => apiCall('/api/dashboard/usage-analytics'),
  getTopEmployeeGroups: () => apiCall('/api/dashboard/top-employee-groups'),
}





// Employees API calls (using existing endpoint)
export const employeesAPI = {
  getAll: () => apiCall('/api/employees'),
  create: (data: any) => apiCall('/api/employees', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiCall(`/api/employees/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiCall(`/api/employees/${id}`, {
    method: 'DELETE',
  }),
}

// ROI Calculation API (using existing endpoint)
export const roiAPI = {
  calculate: (data: any) => apiCall('/api/calculate-roi', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getConfiguration: () => apiCall('/api/roi-configuration'),
  updateConfiguration: (data: any) => apiCall('/api/roi-configuration', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
}

// Authentication API calls
export const authAPI = {
  login: (username: string, password: string) => apiCall('/api/login', {
    method: 'POST',
    body: JSON.stringify({ username, password }),
  }),
  logout: () => apiCall('/api/logout', {
    method: 'POST',
  }),
  checkAuth: () => apiCall('/api/user'),
}

// User API calls
export const userAPI = {
  getCurrentUser: () => apiCall('/api/user'),
  updateProfile: (data: any) => apiCall('/api/user', {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
}

// AI Tools API calls
export const aiToolsAPI = {
  getAll: () => apiCall('/api/ai-tools'),
  create: (data: any) => apiCall('/api/ai-tools', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiCall(`/api/ai-tools/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiCall(`/api/ai-tools/${id}`, {
    method: 'DELETE',
  }),
  getStats: () => apiCall('/api/ai-tools/stats'),
  getScraperData: () => apiCall('/api/ai-tools/scraper-data'),
}

// Employee Management API calls
export const employeeManagementAPI = {
  getEmployeeData: () => apiCall('/api/employee-management'),
  updateAdoptionRate: (jobFamily: string, adoptionRate: number) => apiCall('/api/employee-management/update-adoption', {
    method: 'POST',
    body: JSON.stringify({ job_family: jobFamily, adoption_rate: adoptionRate }),
  }),
  updateEmployee: (employeeId: number, updates: any) => apiCall('/api/employee-management/update-employee', {
    method: 'POST',
    body: JSON.stringify({ employee_id: employeeId, updates }),
  }),
  recalculateAllROI: () => apiCall('/api/employee-management/recalculate-all-roi', {
    method: 'POST',
  }),
  addEmployeeGroups: (groups: any[]) => apiCall('/api/employee-groups/add', {
    method: 'POST',
    body: JSON.stringify({ groups }),
  }),
  splitEmployeeGroup: (sourceJobFamily: string, newJobFamily: string, splitCount: number) => apiCall('/api/employee-groups/split', {
    method: 'POST',
    body: JSON.stringify({
      sourceJobFamily,
      newJobFamily,
      splitCount
    }),
  }),
  getExistingGroups: () => apiCall('/api/employee-groups/existing'),
  updateJobFamilySalary: (jobFamily: string, newAvgSalary: number) => apiCall('/api/employee-groups/update-salary', {
    method: 'POST',
    body: JSON.stringify({
      job_family: jobFamily,
      new_avg_salary: newAvgSalary
    }),
  }),
  updateJobFamilyCount: (jobFamily: string, newCount: number) => apiCall('/api/employee-groups/update-count', {
    method: 'POST',
    body: JSON.stringify({
      job_family: jobFamily,
      new_count: newCount
    }),
  }),
}

// Quick Stats API for sidebar
export const quickStatsAPI = {
  getQuickStats: async () => {
    try {
      const [aiToolsStats, dashboardMetrics] = await Promise.all([
        aiToolsAPI.getStats(),
        dashboardAPI.getMetrics()
      ])

      return {
        total_tools: aiToolsStats.active_tools || 0,
        total_employees: dashboardMetrics.total_employees || 0,
        adoption_rate: aiToolsStats.adoption_rate || 0,
        monthly_spend: aiToolsStats.monthly_spend || 0,
        annual_spend: aiToolsStats.annual_spend || 0
      }
    } catch (error) {
      console.error('Failed to fetch quick stats:', error)
      throw error
    }
  }
}