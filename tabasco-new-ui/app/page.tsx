"use client"

import { useState } from "react"
import { AuthProvider, useAuth } from "@/contexts/AuthContext"
import Sidebar from "@/components/sidebar"
import Dashboard from "@/components/dashboard"

import EmployeeManagement from "@/components/employee-management"
import AIToolsManagement from "@/components/ai-tools-management"
import HRIntegration from "@/components/hr-integration"
import Login from "@/components/login"
import { Loader2 } from "lucide-react"

function AppContent() {
  const [activeView, setActiveView] = useState("dashboard")
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading Tabasco...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <Login />
  }

  const renderContent = () => {
    switch (activeView) {
      case "dashboard":
        return <Dashboard />
      case "employees":
        return <EmployeeManagement />
      case "ai-tools":
        return <AIToolsManagement />
      case "hr-integration":
        return <HRIntegration />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Sidebar activeView={activeView} setActiveView={setActiveView} />
      <main className="flex-1 overflow-auto">{renderContent()}</main>
    </div>
  )
}

export default function TabascoApp() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}
