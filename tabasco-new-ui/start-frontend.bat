@echo off
REM 🎨 Tabasco Frontend Startup Script for Windows
REM This script sets up and starts the Next.js frontend server

echo 🎨 Starting Tabasco Frontend...
echo ================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] package.json not found. Please run this script from the tabasco-new-ui directory.
    pause
    exit /b 1
)

REM 1. Check if .env.local file exists, create from example if not
if not exist ".env.local" (
    echo [WARNING] .env.local file not found
    if exist ".env.example" (
        echo [INFO] Copying .env.example to .env.local...
        copy ".env.example" ".env.local"
        echo [SUCCESS] .env.local file created from example
    ) else (
        echo [ERROR] .env.example not found. Please create .env.local file manually.
        pause
        exit /b 1
    )
)

REM 2. Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm not found. Please install npm.
    pause
    exit /b 1
)

echo [INFO] Node.js version:
node --version
echo [INFO] npm version:
npm --version

REM 3. Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [INFO] Installing Node.js dependencies...
    npm install --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed
) else (
    echo [INFO] Dependencies already installed, skipping...
)

REM 4. Start the Next.js development server
echo [INFO] Starting Next.js frontend server...
echo.
echo [SUCCESS] 🚀 Frontend starting on http://localhost:3000
echo [SUCCESS] 🔗 Backend API: http://localhost:5004
echo [SUCCESS] 📊 Default login credentials:
echo [SUCCESS]    👤 Demo User: demo / demo123
echo [SUCCESS]    🔐 Admin User: admin / admin123
echo.
echo [INFO] Press Ctrl+C to stop the server
echo ================================

REM Start the application
npm run dev

pause
