"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'

interface User {
  id: string
  username: string
  email: string
}

interface AuthContextType {
  user: User | null
  login: (username: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  isLoading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [token, setToken] = useState<string | null>(null)

  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5004'

  // Get token from localStorage
  useEffect(() => {
    const savedToken = localStorage.getItem('tabasco_token')
    if (savedToken) {
      setToken(savedToken)
    }
  }, [])

  // Check if user is already logged in on app start
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const savedToken = localStorage.getItem('tabasco_token')
      if (!savedToken) {
        setIsLoading(false)
        return
      }

      const response = await fetch(`${API_BASE}/api/user`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${savedToken}`,
        },
      })

      if (response.ok) {
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          console.warn('Auth check: Non-JSON response received')
          setUser(null)
          return
        }

        const text = await response.text()
        if (!text.trim()) {
          console.warn('Auth check: Empty response received')
          setUser(null)
          return
        }

        try {
          const userData = JSON.parse(text)
          setUser(userData)
          setToken(savedToken)
          console.log('✅ Auth check: User authenticated with token')
        } catch (parseError) {
          console.error('Auth check: JSON parse error:', parseError)
          localStorage.removeItem('tabasco_token')
          setToken(null)
          setUser(null)
        }
      } else {
        console.log('❌ Auth check: Token invalid, clearing auth')
        localStorage.removeItem('tabasco_token')
        setToken(null)
        setUser(null)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      const response = await fetch(`${API_BASE}/api/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      })

      if (response.ok) {
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          console.warn('Login: Non-JSON response received')
          return false
        }

        const text = await response.text()
        if (!text.trim()) {
          console.warn('Login: Empty response received')
          return false
        }

        try {
          const data = JSON.parse(text)
          if (data.user && data.token) {
            // Store JWT token in localStorage
            localStorage.setItem('tabasco_token', data.token)
            setToken(data.token)
            setUser(data.user)
            console.log('✅ Login successful: Token stored')
            return true
          }
        } catch (parseError) {
          console.error('Login: JSON parse error:', parseError)
          console.error('Login: Response text:', text)
          return false
        }
      }

      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Clear token from localStorage
      localStorage.removeItem('tabasco_token')
      setToken(null)
      setUser(null)
      console.log('✅ Logout successful: Token cleared')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const value: AuthContextType = {
    user,
    login,
    logout,
    isLoading,
    isAuthenticated: !!user,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
