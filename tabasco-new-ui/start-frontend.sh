#!/bin/bash

# 🎨 Tabasco Frontend Startup Script
# This script sets up and starts the Next.js frontend server

set -e  # Exit on any error

echo "🎨 Starting Tabasco Frontend..."
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the tabasco-new-ui directory."
    exit 1
fi

# 1. Check if .env.local file exists, create from example if not
if [ ! -f ".env.local" ]; then
    print_warning ".env.local file not found"
    if [ -f ".env.example" ]; then
        print_status "Copying .env.example to .env.local..."
        cp .env.example .env.local
        print_success ".env.local file created from example"
    else
        print_error ".env.example not found. Please create .env.local file manually."
        exit 1
    fi
fi

# 2. Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js not found. Please install Node.js from https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm not found. Please install npm."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

# 3. Install dependencies if node_modules doesn't exist or package-lock.json is newer
if [ ! -d "node_modules" ] || [ "package-lock.json" -nt "node_modules" ]; then
    print_status "Installing Node.js dependencies..."
    npm install --legacy-peer-deps
    print_success "Dependencies installed"
else
    print_status "Dependencies already installed, skipping..."
fi

# 4. Find available port (try 3000, 3001, 3002, etc.)
find_available_port() {
    local port=3000
    while lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; do
        print_warning "Port $port is in use, trying next port..." >&2
        ((port++))
        if [ $port -gt 3010 ]; then
            print_error "No available ports found between 3000-3010" >&2
            exit 1
        fi
    done
    echo $port
}

FRONTEND_PORT=$(find_available_port)
print_status "Using port: $FRONTEND_PORT"

# 5. Update CORS configuration reminder
if [ $FRONTEND_PORT -ne 3000 ]; then
    print_warning "Frontend will run on port $FRONTEND_PORT instead of 3000"
    print_warning "Make sure backend CORS_ORIGINS includes http://localhost:$FRONTEND_PORT"
fi

# 6. Start the Next.js development server
print_status "Starting Next.js frontend server..."
echo ""
print_success "🚀 Frontend starting on http://localhost:$FRONTEND_PORT"
print_success "🔗 Backend API: http://localhost:5004"
print_success "📊 Default login credentials:"
print_success "   👤 Demo User: demo / demo123"
print_success "   🔐 Admin User: admin / password"
echo ""
print_status "Press Ctrl+C to stop the server"
echo "================================"

# Start the application
npm run dev -- -p $FRONTEND_PORT
