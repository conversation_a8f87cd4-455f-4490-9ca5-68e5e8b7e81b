"use client"

import { useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Settings, User, Bell, Shield, HelpCircle, LogOut, ChevronUp, ChevronDown } from "lucide-react"

export default function UserProfileMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, logout } = useAuth()

  const userData = {
    name: user?.username || "User",
    email: user?.email || "<EMAIL>",
    initials: user?.username?.charAt(0).toUpperCase() || "U",
  }

  const handleLogout = async () => {
    await logout()
  }

  const menuItems = [
    { icon: Settings, label: "Preferences", action: () => console.log("Preferences") },
    { icon: User, label: "Profile", action: () => console.log("Profile") },
    { icon: Bell, label: "Notifications", action: () => console.log("Notifications") },
    { icon: Shield, label: "Privacy & Security", action: () => console.log("Privacy & Security") },
    { icon: HelpCircle, label: "Help & Support", action: () => console.log("Help & Support") },
  ]

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="w-full justify-between p-3 h-auto hover:bg-gray-100/50 transition-colors">
          <div className="flex items-center gap-3">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium">
                {userData.initials}
              </AvatarFallback>
            </Avatar>
            <div className="text-left">
              <p className="text-sm font-medium text-gray-900">{userData.name}</p>
              <p className="text-xs text-gray-500 truncate max-w-[120px]">{userData.email}</p>
            </div>
          </div>
          {isOpen ? <ChevronUp className="h-4 w-4 text-gray-400" /> : <ChevronDown className="h-4 w-4 text-gray-400" />}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="w-64 bg-gray-900 border-gray-700 text-white"
        align="end"
        side="top"
        sideOffset={8}
      >
        {/* User Info Header */}
        <div className="px-3 py-3 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10">
              <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium">
                {userData.initials}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-white">{userData.name}</p>
              <p className="text-sm text-gray-400">{userData.email}</p>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        {menuItems.map((item, index) => {
          const Icon = item.icon
          return (
            <DropdownMenuItem
              key={index}
              onClick={item.action}
              className="px-3 py-3 cursor-pointer hover:bg-gray-800 focus:bg-gray-800 text-white border-b border-gray-700 last:border-b-0"
            >
              <Icon className="h-4 w-4 mr-3 text-gray-300" />
              <span className="text-sm">{item.label}</span>
            </DropdownMenuItem>
          )
        })}

        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Sign Out */}
        <DropdownMenuItem
          onClick={handleLogout}
          className="px-3 py-3 cursor-pointer hover:bg-gray-800 focus:bg-gray-800 text-red-400 hover:text-red-300"
        >
          <LogOut className="h-4 w-4 mr-3" />
          <span className="text-sm">Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
