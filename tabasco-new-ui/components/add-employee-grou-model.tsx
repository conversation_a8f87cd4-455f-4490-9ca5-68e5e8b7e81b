"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Plus, Trash2, Copy, Users, DollarSign } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface EmployeeGroup {
  id: string
  jobFamilyName: string
  employeeCount: number
  avgSalary: number
  level: string
}

interface AddEmployeeGroupModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (groups: EmployeeGroup[], splitOperations?: any[]) => void
  existingGroups: any[]
  hrIntegrationEnabled?: boolean
  hasManualData?: boolean
  allowManualAdditions?: boolean
}

export default function AddEmployeeGroupModal({
  isOpen,
  onClose,
  onSubmit,
  existingGroups,
  hrIntegrationEnabled = false,
  hasManualData = false,
  allowManualAdditions = true,
}: AddEmployeeGroupModalProps) {
  const [groups, setGroups] = useState<EmployeeGroup[]>([
    {
      id: "1",
      jobFamilyName: "",
      employeeCount: 0,
      avgSalary: 0,
      level: "",
    },
  ])

  const [selectedExistingGroup, setSelectedExistingGroup] = useState<string | null>(null)
  const [splitCount, setSplitCount] = useState<number>(0)
  const [newJobFamilyName, setNewJobFamilyName] = useState<string>("")
  const [activeTab, setActiveTab] = useState<string>("new")
  const [splitOperations, setSplitOperations] = useState<any[]>([])

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setGroups([
        {
          id: "1",
          jobFamilyName: "",
          employeeCount: 0,
          avgSalary: 0,
          level: "",
        },
      ])
      setSelectedExistingGroup(null)
      setSplitCount(0)
      setNewJobFamilyName("")
      setActiveTab("new")
      setSplitOperations([])
    }
  }, [isOpen])

  const jobFamilyOptions = [
    "Software Engineer",
    "Frontend Engineer",
    "Backend Engineer",
    "Full Stack Engineer",
    "Product Manager",
    "Senior Product Manager",
    "Data Scientist",
    "Senior Data Scientist",
    "Designer",
    "UX Designer",
    "UI Designer",
    "Research Scientist",
    "DevOps Engineer",
    "QA Engineer",
    "Marketing Manager",
    "Sales Representative",
    "Custom Role",
  ]

  const levelOptions = ["L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8", "L9"]

  const addNewGroup = () => {
    const newGroup: EmployeeGroup = {
      id: Date.now().toString(),
      jobFamilyName: "",
      employeeCount: 0,
      avgSalary: 0,
      level: "",
    }
    setGroups([...groups, newGroup])
  }

  const removeGroup = (id: string) => {
    if (groups.length > 1) {
      setGroups(groups.filter((group) => group.id !== id))
    }
  }

  const updateGroup = (id: string, field: keyof EmployeeGroup, value: any) => {
    setGroups(groups.map((group) => (group.id === id ? { ...group, [field]: value } : group)))
  }

  const duplicateGroup = (id: string) => {
    const groupToDuplicate = groups.find((g) => g.id === id)
    if (groupToDuplicate) {
      const newGroup = {
        ...groupToDuplicate,
        id: Date.now().toString(),
        jobFamilyName: `${groupToDuplicate.jobFamilyName} (Copy)`,
      }
      setGroups([...groups, newGroup])
    }
  }

  const handleSplitGroup = () => {
    if (!selectedExistingGroup || !newJobFamilyName.trim() || splitCount <= 0) {
      console.log('❌ Split validation failed:', { selectedExistingGroup, newJobFamilyName, splitCount })
      return
    }

    const existingGroup = existingGroups.find((g) => g.id === selectedExistingGroup)
    if (!existingGroup || splitCount >= existingGroup.count) {
      console.log('❌ Existing group validation failed:', { existingGroup, splitCount })
      return
    }

    console.log('🔄 Creating split group:', {
      from: existingGroup.name,
      to: newJobFamilyName,
      count: splitCount
    })

    // Create new group with split employees - use the new job family name and inherit salary/level
    const newGroup: EmployeeGroup = {
      id: Date.now().toString(),
      jobFamilyName: newJobFamilyName.trim(), // Use the name specified in the split form
      employeeCount: splitCount,
      avgSalary: Number.parseInt(existingGroup.avgSalary.replace("$", "").replace("K", "")) * 1000,
      level: existingGroup.level || "L5",
    }

    // Track the split operation for backend processing
    const splitOperation = {
      sourceJobFamily: existingGroup.name,
      newJobFamily: newJobFamilyName.trim(),
      splitCount: splitCount,
      newGroupId: newGroup.id // Link to the new group for reference
    }

    console.log('✅ Created new group:', newGroup)
    console.log('✅ Created split operation:', splitOperation)

    // Add the new group to the groups list
    setGroups([...groups, newGroup])

    // Track the split operation
    setSplitOperations([...splitOperations, splitOperation])

    // Reset split form
    setSelectedExistingGroup(null)
    setSplitCount(0)
    setNewJobFamilyName("")

    // Switch to new tab to see the added group and allow editing
    setActiveTab("new")

    console.log('🔄 Switched to new tab, group should be visible')
  }

  const handleSubmit = () => {
    const validGroups = groups.filter((group) => group.jobFamilyName && group.employeeCount > 0 && group.avgSalary > 0)
    if (validGroups.length > 0) {
      // Pass both groups and split operations to parent
      onSubmit(validGroups, splitOperations)

      // Reset form
      setGroups([
        {
          id: "1",
          jobFamilyName: "",
          employeeCount: 0,
          avgSalary: 0,
          level: "",
        },
      ])
      setSplitOperations([])
      onClose()
    }
  }

  const getTotalEmployees = () => groups.reduce((sum, group) => sum + group.employeeCount, 0)
  const getTotalCosts = () => groups.reduce((sum, group) => sum + group.avgSalary * group.employeeCount, 0)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {hrIntegrationEnabled ? 'Employee Group Management' : 'Add Employee Groups'}
          </DialogTitle>
          <DialogDescription>
            {hrIntegrationEnabled && !allowManualAdditions
              ? 'HR Integration is enabled. You can only split existing groups. To add new employee groups, use your HR system.'
              : hrIntegrationEnabled && allowManualAdditions
              ? 'HR Integration is active. You can split existing groups or add manual groups for special cases (contractors, consultants, etc.).'
              : 'Add new employee groups or split existing ones into sub-groups.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* HR Integration Info */}
          {hrIntegrationEnabled && (
            <Card className={`${allowManualAdditions ? 'bg-blue-50 border-blue-200' : 'bg-amber-50 border-amber-200'}`}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className={`h-8 w-8 rounded-full ${allowManualAdditions ? 'bg-blue-100' : 'bg-amber-100'} flex items-center justify-center`}>
                    <Users className={`h-4 w-4 ${allowManualAdditions ? 'text-blue-600' : 'text-amber-600'}`} />
                  </div>
                  <div>
                    <h3 className={`font-medium ${allowManualAdditions ? 'text-blue-900' : 'text-amber-900'}`}>
                      HR Integration Active
                      {hasManualData && ' (Hybrid Mode)'}
                    </h3>
                    <p className={`text-sm ${allowManualAdditions ? 'text-blue-700' : 'text-amber-700'}`}>
                      {allowManualAdditions
                        ? 'Primary employee data comes from HR system. You can also add manual groups for contractors, consultants, or temporary roles not in your HR system.'
                        : 'Employee data is managed through your HR system. You can only split existing groups here. To add new employee groups, please use your HR system and sync the data.'
                      }
                    </p>
                    {hasManualData && (
                      <p className="text-xs text-gray-600 mt-1">
                        💡 You have both HR-synced and manually entered employee data.
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          <div className="grid grid-cols-4 gap-4">
            <Card className="bg-white/60 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{groups.length}</div>
                <div className="text-sm text-gray-600">Groups to Add</div>
              </CardContent>
            </Card>
            <Card className="bg-white/60 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{splitOperations.length}</div>
                <div className="text-sm text-gray-600">Split Operations</div>
              </CardContent>
            </Card>
            <Card className="bg-white/60 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{getTotalEmployees()}</div>
                <div className="text-sm text-gray-600">Total Employees</div>
              </CardContent>
            </Card>
            <Card className="bg-white/60 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">${(getTotalCosts() / 1000000).toFixed(1)}M</div>
                <div className="text-sm text-gray-600">Total Annual Cost</div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs for New Group vs Split Group */}
          <Tabs value={hrIntegrationEnabled && !allowManualAdditions ? "split" : activeTab} onValueChange={setActiveTab}>
            <TabsList className={`mb-4 ${hrIntegrationEnabled && !allowManualAdditions ? 'grid-cols-1' : 'grid-cols-2'} grid`}>
              {(!hrIntegrationEnabled || allowManualAdditions) && (
                <TabsTrigger value="new">
                  {hrIntegrationEnabled ? 'Manual Groups' : 'New Groups'}
                </TabsTrigger>
              )}
              <TabsTrigger value="split">Split Existing Group</TabsTrigger>
            </TabsList>

            {(!hrIntegrationEnabled || allowManualAdditions) && <TabsContent value="new" className="space-y-4">
              {/* Employee Groups */}
              {groups.map((group, index) => (
                <Card key={group.id} className="bg-white border-gray-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Group {index + 1}</CardTitle>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => duplicateGroup(group.id)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        {groups.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeGroup(group.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Job Family Name */}
                      <div className="space-y-2">
                        <Label htmlFor={`jobFamily-${group.id}`}>Job Family Name *</Label>
                        {/* Check if this group came from a split - if so, use Input instead of Select */}
                        {splitOperations.some(op => op.newGroupId === group.id) ? (
                          <Input
                            id={`jobFamily-${group.id}`}
                            placeholder="Job Family Name"
                            value={group.jobFamilyName}
                            onChange={(e) => updateGroup(group.id, "jobFamilyName", e.target.value)}
                          />
                        ) : (
                          <Select
                            value={group.jobFamilyName}
                            onValueChange={(value) => updateGroup(group.id, "jobFamilyName", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select or type role" />
                            </SelectTrigger>
                            <SelectContent>
                              {jobFamilyOptions.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* Employee Count */}
                      <div className="space-y-2">
                        <Label htmlFor={`count-${group.id}`}>Number of Employees *</Label>
                        <Input
                          id={`count-${group.id}`}
                          type="number"
                          placeholder="e.g., 25"
                          value={group.employeeCount || ""}
                          onChange={(e) => updateGroup(group.id, "employeeCount", Number.parseInt(e.target.value) || 0)}
                        />
                      </div>

                      {/* Level */}
                      <div className="space-y-2">
                        <Label htmlFor={`level-${group.id}`}>Average Level</Label>
                        <Select value={group.level} onValueChange={(value) => updateGroup(group.id, "level", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select level" />
                          </SelectTrigger>
                          <SelectContent>
                            {levelOptions.map((level) => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Average Salary */}
                      <div className="space-y-2">
                        <Label htmlFor={`salary-${group.id}`}>Average Annual Salary *</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id={`salary-${group.id}`}
                            type="number"
                            placeholder="165000"
                            className="pl-10"
                            value={group.avgSalary || ""}
                            onChange={(e) => updateGroup(group.id, "avgSalary", Number.parseInt(e.target.value) || 0)}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Group Status */}
                    <div className="flex gap-2 flex-wrap">
                      {/* HR Integration indicator for manual groups */}
                      {hrIntegrationEnabled && !splitOperations.some(op => op.newGroupId === group.id) && (
                        <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                          Manual Entry
                        </Badge>
                      )}
                      {/* Check if this group came from a split */}
                      {splitOperations.some(op => op.newGroupId === group.id) && (
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                          Split from {splitOperations.find(op => op.newGroupId === group.id)?.sourceJobFamily}
                        </Badge>
                      )}
                      {group.jobFamilyName && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {group.jobFamilyName}
                        </Badge>
                      )}
                      {group.employeeCount > 0 && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {group.employeeCount} employees
                        </Badge>
                      )}
                      {group.avgSalary > 0 && (
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          ${(group.avgSalary / 1000).toFixed(0)}K avg
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}

              <Button
                variant="outline"
                onClick={addNewGroup}
                className="w-full border-dashed border-gray-300 hover:border-gray-400"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another Group
              </Button>
            </TabsContent>}

            <TabsContent value="split" className="space-y-4">
              <Card className="bg-white border-gray-200">
                <CardHeader>
                  <CardTitle>Split Existing Group</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Select Existing Group */}
                  <div className="space-y-2">
                    <Label htmlFor="existingGroup">Select Existing Group *</Label>
                    <Select value={selectedExistingGroup || ""} onValueChange={setSelectedExistingGroup}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a group to split" />
                      </SelectTrigger>
                      <SelectContent>
                        {existingGroups.map((group) => (
                          <SelectItem key={group.id} value={group.id}>
                            {group.name} ({group.count} employees)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedExistingGroup && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* New Job Family Name */}
                        <div className="space-y-2">
                          <Label htmlFor="newJobFamily">New Job Family Name *</Label>
                          <Input
                            id="newJobFamily"
                            placeholder="e.g., Frontend Engineer"
                            value={newJobFamilyName}
                            onChange={(e) => setNewJobFamilyName(e.target.value)}
                          />
                        </div>

                        {/* Number of Employees to Split */}
                        <div className="space-y-2">
                          <Label htmlFor="splitCount">Number of Employees to Move *</Label>
                          <Input
                            id="splitCount"
                            type="number"
                            placeholder="e.g., 10"
                            value={splitCount || ""}
                            onChange={(e) => setSplitCount(Number.parseInt(e.target.value) || 0)}
                          />
                          {selectedExistingGroup && (
                            <p className="text-xs text-gray-500 mt-1">
                              Max: {existingGroups.find((g) => g.id === selectedExistingGroup)?.count - 1} employees
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="pt-2">
                        <Button
                          onClick={handleSplitGroup}
                          disabled={
                            !selectedExistingGroup ||
                            !newJobFamilyName ||
                            splitCount <= 0 ||
                            splitCount >= (existingGroups.find((g) => g.id === selectedExistingGroup)?.count || 0)
                          }
                          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                        >
                          Split Group
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="bg-gradient-to-r from-red-600 to-red-700 text-white"
              disabled={
                hrIntegrationEnabled && !allowManualAdditions
                  ? splitOperations.length === 0
                  : groups.every((g) => !g.jobFamilyName || g.employeeCount === 0) && splitOperations.length === 0
              }
            >
              {(() => {
                const validGroups = groups.filter((g) => g.jobFamilyName && g.employeeCount > 0).length
                const splits = splitOperations.length

                if (hrIntegrationEnabled && !allowManualAdditions) {
                  return `Process ${splits} Split Operation(s)`
                } else if (hrIntegrationEnabled && allowManualAdditions) {
                  if (validGroups > 0 && splits > 0) {
                    return `Add ${validGroups} Group(s) & Process ${splits} Split(s)`
                  } else if (validGroups > 0) {
                    return `Add ${validGroups} Manual Group(s)`
                  } else if (splits > 0) {
                    return `Process ${splits} Split Operation(s)`
                  }
                  return 'No Operations'
                } else {
                  return `Add ${validGroups} Group(s)`
                }
              })()}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
