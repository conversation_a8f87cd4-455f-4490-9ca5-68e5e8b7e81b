"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Building, CheckCircle, Plus, Download } from "lucide-react"

export default function HRIntegration() {
  const hrSystems = [
    { name: "ADP", logo: "🏢", connected: false, employees: 0 },
    { name: "Rippling", logo: "🌊", connected: false, employees: 0 }, // Disabled - was true
    { name: "BambooHR", logo: "🎋", connected: false, employees: 0 },
    { name: "Paylocity", logo: "💰", connected: false, employees: 0 },
  ]

  const integrationStats = [
    { title: "Connected Systems", value: "0", change: "of 4", icon: Building, color: "gray" },
    { title: "Synced Employees", value: "0", change: "disabled", icon: CheckCircle, color: "gray" },
    { title: "Data Accuracy", value: "N/A", change: "disabled", icon: CheckCircle, color: "gray" },
    { title: "Last Sync", value: "N/A", change: "disabled", icon: CheckCircle, color: "gray" },
  ]

  return (
    <div className="p-8 space-y-8 opacity-75">
      {/* Disabled Banner */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center">
            <Building className="h-4 w-4 text-yellow-600" />
          </div>
          <div>
            <h3 className="font-medium text-yellow-800">HR Integration Disabled</h3>
            <p className="text-sm text-yellow-700">
              HR integration features are currently disabled. Use manual employee management instead.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">HR Integration</h1>
          <p className="text-gray-600 mt-1">Connect and sync with your HR systems</p>
        </div>
        <div className="flex gap-4">
          <Button variant="outline" className="border-gray-300" disabled>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button className="bg-gray-400 text-white cursor-not-allowed" disabled>
            <Plus className="h-4 w-4 mr-2" />
            Add Integration
          </Button>
        </div>
      </div>

      {/* Integration Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {integrationStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index} className="bg-white/60 backdrop-blur-sm border-gray-200/50 opacity-60">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-xl bg-gray-400 flex items-center justify-center">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-gray-500">{stat.change}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* HR Systems */}
      <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50 opacity-60">
        <CardHeader>
          <CardTitle className="text-gray-500">Available HR Systems (Disabled)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {hrSystems.map((system, index) => (
              <div
                key={index}
                className="p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-not-allowed"
              >
                <div className="text-center">
                  <div className="text-4xl mb-3 opacity-50">{system.logo}</div>
                  <h3 className="font-semibold text-gray-500 mb-2">{system.name}</h3>
                  <div className="space-y-2">
                    <Badge variant="outline" className="text-gray-400 border-gray-300">
                      Disabled
                    </Badge>
                    <p className="text-sm text-gray-400">Integration disabled</p>
                    <Button size="sm" className="w-full bg-gray-300 text-gray-500 cursor-not-allowed" disabled>
                      Connect
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Sync Activity */}
      <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50 opacity-60">
        <CardHeader>
          <CardTitle className="text-gray-500">Recent Sync Activity (Disabled)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Building className="h-6 w-6 text-gray-400" />
                </div>
                <p className="font-medium text-gray-500 mb-1">No Sync Activity</p>
                <p className="text-sm text-gray-400">HR integration is currently disabled</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
