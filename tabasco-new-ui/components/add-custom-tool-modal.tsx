"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DollarSign, Plus, Users } from "lucide-react"

interface CustomTool {
  name: string
  provider: string
  price: number
  pricingModel: "per user" | "per license" | "per month"
  description: string
  employeeCount: number
  category: string
}

interface AddCustomToolModalProps {
  isOpen: boolean
  onClose: () => void
  onAddTool: (tool: CustomTool) => void
}

export default function AddCustomToolModal({ isOpen, onClose, onAddTool }: AddCustomToolModalProps) {
  const [tool, setTool] = useState<CustomTool>({
    name: "",
    provider: "",
    price: 0,
    pricingModel: "per user",
    description: "",
    employeeCount: 0,
    category: "Custom Tool",
  })

  const handleSubmit = () => {
    if (tool.name && tool.price > 0 && tool.employeeCount > 0) {
      onAddTool(tool)
      setTool({
        name: "",
        provider: "",
        price: 0,
        pricingModel: "per user",
        description: "",
        employeeCount: 0,
        category: "Custom Tool",
      })
      onClose()
    }
  }

  const updateTool = (field: keyof CustomTool, value: any) => {
    setTool((prev) => ({ ...prev, [field]: value }))
  }

  const calculateMonthlyCost = () => {
    if (tool.pricingModel === "per month") {
      return tool.price
    }
    return tool.price * tool.employeeCount
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Custom Tool
          </DialogTitle>
          <DialogDescription>Add a custom AI tool with your own pricing information.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="toolName">Tool Name *</Label>
            <Input
              id="toolName"
              placeholder="e.g., Custom AI Assistant"
              value={tool.name}
              onChange={(e) => updateTool("name", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="provider">Provider</Label>
            <Input
              id="provider"
              placeholder="e.g., Internal Team"
              value={tool.provider}
              onChange={(e) => updateTool("provider", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Brief description of the tool"
              value={tool.description}
              onChange={(e) => updateTool("description", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={tool.category} onValueChange={(value) => updateTool("category", value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Custom Tool">Custom Tool</SelectItem>
                <SelectItem value="General AI Assistant">General AI Assistant</SelectItem>
                <SelectItem value="Code Generation">Code Generation</SelectItem>
                <SelectItem value="Content Creation">Content Creation</SelectItem>
                <SelectItem value="Image Generation">Image Generation</SelectItem>
                <SelectItem value="Writing Assistant">Writing Assistant</SelectItem>
                <SelectItem value="Productivity">Productivity</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">Price *</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="price"
                  type="number"
                  placeholder="20"
                  className="pl-10"
                  value={tool.price || ""}
                  onChange={(e) => updateTool("price", Number.parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pricingModel">Pricing Model</Label>
              <Select value={tool.pricingModel} onValueChange={(value) => updateTool("pricingModel", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="per user">Per User</SelectItem>
                  <SelectItem value="per license">Per License</SelectItem>
                  <SelectItem value="per month">Per Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="employeeCount">Number of Employees Using Tool *</Label>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="employeeCount"
                type="number"
                placeholder="e.g., 25"
                className="pl-10"
                value={tool.employeeCount || ""}
                onChange={(e) => updateTool("employeeCount", Number.parseInt(e.target.value) || 0)}
              />
            </div>
          </div>

          {/* Cost Preview */}
          {tool.price > 0 && tool.employeeCount > 0 && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-sm text-blue-800">
                <div className="font-medium">Cost Preview:</div>
                <div className="mt-1">
                  Monthly Cost: <span className="font-bold">${calculateMonthlyCost().toLocaleString()}</span>
                </div>
                <div>
                  Annual Cost: <span className="font-bold">${(calculateMonthlyCost() * 12).toLocaleString()}</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!tool.name || tool.price <= 0 || tool.employeeCount <= 0}
              className="flex-1 bg-gradient-to-r from-red-600 to-red-700 text-white"
            >
              Add Tool
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
