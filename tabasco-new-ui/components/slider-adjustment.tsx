"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { Card, CardContent } from "@/components/ui/card"
import { Check, X } from "lucide-react"

interface SliderAdjustmentProps {
  currentValue: string
  onValueChange: (newValue: string) => void
  onClose: () => void
  position: { x: number; y: number }
}

export default function SliderAdjustment({ currentValue, onValueChange, onClose, position }: SliderAdjustmentProps) {
  const currentPercent = Number.parseInt(currentValue.replace("%", ""))
  const [sliderValue, setSliderValue] = useState([currentPercent])
  const cardRef = useRef<HTMLDivElement>(null)

  // Close on click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [onClose])

  const handleApply = () => {
    onValueChange(`${sliderValue[0]}%`)
    onClose()
  }

  const handleCancel = () => {
    setSliderValue([currentPercent])
    onClose()
  }

  // Calculate adjustment from original value
  const adjustment = sliderValue[0] - currentPercent
  const adjustmentText = adjustment > 0 ? `+${adjustment}%` : adjustment < 0 ? `${adjustment}%` : "No change"

  return (
    <div
      className="fixed z-50"
      style={{
        left: position.x,
        top: position.y,
        transform: "translate(-50%, -100%)",
      }}
    >
      <Card ref={cardRef} className="w-80 bg-white shadow-xl border-2 border-gray-200">
        <CardContent className="p-4 space-y-4">
          <div className="text-center">
            <h3 className="font-semibold text-gray-900">Adjust AI Adoption</h3>
            <p className="text-sm text-gray-600">Current: {currentValue}</p>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-sm text-gray-600">
              <span>0%</span>
              <span className="font-medium">New Value: {sliderValue[0]}%</span>
              <span>100%</span>
            </div>

            <Slider value={sliderValue} onValueChange={setSliderValue} max={100} min={0} step={25} className="w-full" />

            {/* Step indicators */}
            <div className="flex justify-between text-xs text-gray-400">
              <span>0%</span>
              <span>25%</span>
              <span>50%</span>
              <span>75%</span>
              <span>100%</span>
            </div>
          </div>

          <div className="text-center">
            <div
              className={`text-sm font-medium ${
                adjustment > 0 ? "text-green-600" : adjustment < 0 ? "text-red-600" : "text-gray-600"
              }`}
            >
              {adjustmentText}
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleApply}
              className="flex-1 bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800"
            >
              <Check className="h-4 w-4 mr-2" />
              Apply
            </Button>
            <Button onClick={handleCancel} variant="outline" className="flex-1">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
