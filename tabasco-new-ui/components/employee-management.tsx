"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, TrendingUp, DollarSign, Plus, Loader2 } from "lucide-react"
import { employeeManagementAPI, aiToolsAPI } from "@/lib/api"
import SliderAdjustment from "@/components/slider-adjustment"
import AddEmployeeGroupModal from "@/components/add-employee-grou-model"
import EditableSalary from "@/components/editable-salary"
import EditableCount from "@/components/editable-count"

interface EmployeeStats {
  total_employees: number
  average_salary: string
  ai_adoption_rate: string
  productivity_gain: string
}

interface JobFamily {
  name: string
  count: number
  avgSalary: string
  aiAdoption: string
  roi: string
  savings?: string
}

interface EmployeeData {
  employee_stats: EmployeeStats
  job_families: JobFamily[]
}

export default function EmployeeManagement() {
  const [employeeData, setEmployeeData] = useState<EmployeeData | null>(null)
  const [aiToolsData, setAiToolsData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sliderPopup, setSliderPopup] = useState<{
    isOpen: boolean
    jobFamily: string
    currentValue: string
    position: { x: number; y: number }
  } | null>(null)
  const [isAddGroupModalOpen, setIsAddGroupModalOpen] = useState(false)
  const [existingGroups, setExistingGroups] = useState<any[]>([])
  const [hrIntegrationEnabled, setHrIntegrationEnabled] = useState(false)
  const [hasManualData, setHasManualData] = useState(false)
  const [allowManualAdditions, setAllowManualAdditions] = useState(true)

  useEffect(() => {
    fetchEmployeeData()
  }, [])



  const fetchEmployeeData = async () => {
    try {
      setLoading(true)

      // Fetch both employee data and AI tools data
      const [data, aiTools] = await Promise.all([
        employeeManagementAPI.getEmployeeData(),
        aiToolsAPI.getAll()
      ])

      // Set AI tools data
      setAiToolsData(aiTools || [])

      // Check if there are active AI tools
      const hasActiveAITools = aiTools && aiTools.some((tool: any) => tool.status === 'active')
      console.log('Employee Management - Active AI Tools Check:', {
        totalTools: aiTools?.length || 0,
        activeTools: aiTools?.filter((tool: any) => tool.status === 'active').length || 0,
        hasActiveAITools
      })

      // Recalculate stats immediately after loading to ensure accurate productivity gain
      if (data && data.job_families && data.job_families.length > 0) {
        console.log('🔄 Recalculating stats on initial load...')
        const updatedStats = recalculateEmployeeStats(data.job_families, hasActiveAITools)

        setEmployeeData({
          employee_stats: updatedStats,
          job_families: data.job_families
        })
        console.log('✅ Stats recalculated on load:', updatedStats)
      } else {
        setEmployeeData(data)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleAdoptionClick = (event: React.MouseEvent, jobFamily: string, currentValue: string) => {
    const rect = event.currentTarget.getBoundingClientRect()
    setSliderPopup({
      isOpen: true,
      jobFamily,
      currentValue,
      position: {
        x: rect.left + rect.width / 2,
        y: rect.top
      }
    })
  }

  const handleSliderValueChange = async (jobFamily: string, newValue: string) => {
    if (employeeData) {
      // Update the adoption rate in the backend and recalculate ROI
      try {
        const adoptionRate = parseInt(newValue.replace('%', ''))
        const updatedData = await employeeManagementAPI.updateAdoptionRate(jobFamily, adoptionRate)

        // Update the local state with recalculated ROI and savings
        const updatedJobFamilies = employeeData.job_families.map(family =>
          family.name === jobFamily
            ? {
                ...family,
                aiAdoption: newValue,
                roi: updatedData.new_roi,
                savings: updatedData.new_savings
              }
            : family
        )

        // Check if there are active AI tools
        const hasActiveAITools = aiToolsData && aiToolsData.some((tool: any) => tool.status === 'active')

        // Recalculate employee statistics
        const updatedStats = recalculateEmployeeStats(updatedJobFamilies, hasActiveAITools)

        setEmployeeData({
          employee_stats: updatedStats,
          job_families: updatedJobFamilies
        })

        console.log(`✅ Updated ${jobFamily}: ${newValue} adoption → ${updatedData.new_roi} ROI`)

      } catch (error) {
        console.error('Failed to update adoption rate:', error)
        // Fallback to local update only
        const updatedJobFamilies = employeeData.job_families.map(family =>
          family.name === jobFamily
            ? { ...family, aiAdoption: newValue }
            : family
        )

        // Check if there are active AI tools
        const hasActiveAITools = aiToolsData && aiToolsData.some((tool: any) => tool.status === 'active')

        // Recalculate employee statistics even in fallback
        const updatedStats = recalculateEmployeeStats(updatedJobFamilies, hasActiveAITools)

        setEmployeeData({
          employee_stats: updatedStats,
          job_families: updatedJobFamilies
        })
      }
    }
  }

  const handleSliderClose = () => {
    setSliderPopup(null)
  }

  const handleAddGroupClick = async () => {
    try {
      // Fetch existing groups for splitting functionality
      const existingGroupsData = await employeeManagementAPI.getExistingGroups()
      setExistingGroups(existingGroupsData.existing_groups || [])
      setIsAddGroupModalOpen(true)
    } catch (error) {
      console.error('Failed to fetch existing groups:', error)
      setIsAddGroupModalOpen(true) // Open modal anyway, just without split functionality
    }
  }

  const handleAddGroupSubmit = async (groups: any[], splitOperations?: any[]) => {
    try {
      setLoading(true)

      // Process split operations first
      if (splitOperations && splitOperations.length > 0) {
        console.log('🔄 Processing split operations:', splitOperations)

        for (const splitOp of splitOperations) {
          await employeeManagementAPI.splitEmployeeGroup(
            splitOp.sourceJobFamily,
            splitOp.newJobFamily,
            splitOp.splitCount
          )
        }
      }

      // Filter out groups that were created from splits (they're already processed)
      const splitGroupIds = splitOperations?.map(op => op.newGroupId) || []
      const newGroups = groups.filter(group => !splitGroupIds.includes(group.id))

      // Process new groups (not from splits)
      if (newGroups.length > 0) {
        console.log('➕ Adding new employee groups:', newGroups)
        const result = await employeeManagementAPI.addEmployeeGroups(newGroups)
        console.log('✅ Successfully added employee groups:', result)
      }

      // Refresh employee data to show all changes
      await fetchEmployeeData()

      setIsAddGroupModalOpen(false)
    } catch (error) {
      console.error('Failed to process employee groups:', error)
      setError(error instanceof Error ? error.message : 'Failed to process employee groups')
    } finally {
      setLoading(false)
    }
  }

  const handleAddGroupClose = () => {
    setIsAddGroupModalOpen(false)
  }

  // Helper function to get base productivity rate by job family
  const getJobFamilyBaseProductivity = (jobFamily: string): number => {
    const baseRates: { [key: string]: number } = {
      'Software Engineer': 25,      // High AI benefit (coding, debugging, testing)
      'Data Scientist': 30,         // Very high AI benefit (analysis, modeling)
      'Product Manager': 15,        // Medium AI benefit (research, documentation)
      'Designer': 20,               // Medium-high AI benefit (ideation, prototyping)
      'Marketing Manager': 12,      // Lower AI benefit (content, analysis)
      'Sales Representative': 8,    // Lowest AI benefit (relationship-based)
      'QA Engineer': 22,           // High AI benefit (test automation, bug detection)
      'DevOps Engineer': 28,       // Very high AI benefit (automation, monitoring)
      'Business Analyst': 18,      // Medium-high AI benefit (data analysis, reporting)
      'Customer Support': 15       // Medium AI benefit (chatbots, knowledge base)
    }
    return baseRates[jobFamily] || 15 // Default 15% for unknown job families
  }

  // Helper function to recalculate employee statistics
  const recalculateEmployeeStats = (jobFamilies: any[], hasActiveAITools: boolean = true) => {
    let totalEmployees = 0
    let totalSalaryWeighted = 0
    let totalAdoptionWeighted = 0
    let totalProductivityWeighted = 0

    console.log(`Employee Management - Recalculating stats with AI Tools: ${hasActiveAITools}`)

    jobFamilies.forEach(family => {
      const count = family.count
      const avgSalary = parseFloat(family.avgSalary.replace(/[$K,]/g, '')) * 1000
      const adoption = parseFloat(family.aiAdoption.replace('%', ''))

      let actualProductivity = 0

      // Only calculate productivity gains if there are active AI tools
      if (hasActiveAITools) {
        // Calculate actual productivity gain based on AI adoption
        const baseProductivity = getJobFamilyBaseProductivity(family.name)
        actualProductivity = baseProductivity * (adoption / 100)
      } else {
        // No AI tools enabled = no productivity gains
        actualProductivity = 0
      }

      totalEmployees += count
      totalSalaryWeighted += avgSalary * count
      totalAdoptionWeighted += adoption * count
      totalProductivityWeighted += actualProductivity * count

      console.log(`Employee Management - ${family.name}: AI Tools=${hasActiveAITools ? 'Yes' : 'No'}, Productivity=${actualProductivity.toFixed(1)}%`)
    })

    const avgSalary = totalEmployees > 0 ? totalSalaryWeighted / totalEmployees : 0
    const avgAdoption = totalEmployees > 0 ? totalAdoptionWeighted / totalEmployees : 0
    const avgProductivity = totalEmployees > 0 ? totalProductivityWeighted / totalEmployees : 0

    return {
      total_employees: totalEmployees,
      average_salary: `$${(avgSalary / 1000).toFixed(0)}K`,
      ai_adoption_rate: `${avgAdoption.toFixed(0)}%`,
      productivity_gain: `${avgProductivity.toFixed(1)}%`
    }
  }

  const handleSalaryUpdate = (jobFamily: string, newSalary: string, newRoi: string, newSavings: string) => {
    try {
      console.log(`🔄 Updating UI for ${jobFamily}: salary=${newSalary}, roi=${newRoi}, savings=${newSavings}`)

      if (!employeeData) {
        console.error('No employee data available')
        return
      }

      // Update the job families data with new values
      const updatedJobFamilies = employeeData.job_families.map(family =>
        family.name === jobFamily
          ? {
              ...family,
              avgSalary: newSalary,
              roi: newRoi,
              savings: newSavings
            }
          : family
      )

      // Check if there are active AI tools
      const hasActiveAITools = aiToolsData && aiToolsData.some((tool: any) => tool.status === 'active')

      // Recalculate employee statistics
      const updatedStats = recalculateEmployeeStats(updatedJobFamilies, hasActiveAITools)

      // Update the employee data state with both job families and stats
      const newEmployeeData = {
        employee_stats: updatedStats,
        job_families: updatedJobFamilies
      }

      setEmployeeData(newEmployeeData)

      // Save updated data to backend for dashboard
      saveToDashboard(newEmployeeData)

      console.log('✅ UI and stats updated successfully')
    } catch (error) {
      console.error('Failed to update UI:', error)
      setError('Failed to update display after salary change')
    }
  }

  const handleCountUpdate = (jobFamily: string, newCount: number, newSalary: string, newRoi: string, newSavings: string) => {
    try {
      console.log(`🔄 Updating UI for ${jobFamily}: count=${newCount}, salary=${newSalary}, roi=${newRoi}, savings=${newSavings}`)

      if (!employeeData) {
        console.error('No employee data available')
        return
      }

      // Update the job families data with new values
      const updatedJobFamilies = employeeData.job_families.map(family =>
        family.name === jobFamily
          ? {
              ...family,
              count: newCount,
              avgSalary: newSalary,
              roi: newRoi,
              savings: newSavings
            }
          : family
      )

      // Check if there are active AI tools
      const hasActiveAITools = aiToolsData && aiToolsData.some((tool: any) => tool.status === 'active')

      // Recalculate employee statistics
      const updatedStats = recalculateEmployeeStats(updatedJobFamilies, hasActiveAITools)

      // Update the employee data state with both job families and stats
      const newEmployeeData = {
        employee_stats: updatedStats,
        job_families: updatedJobFamilies
      }

      setEmployeeData(newEmployeeData)

      // Save updated data to backend for dashboard
      saveToDashboard(newEmployeeData)

      console.log('✅ UI and stats updated successfully')
    } catch (error) {
      console.error('Failed to update UI:', error)
      setError('Failed to update display after employee count change')
    }
  }

  // Function to save core values to backend (only persist user inputs)
  const saveToDashboard = async (data: any) => {
    try {
      console.log('💾 Saving core employee data to backend...')

      // Extract only the core user-input values (not calculated values)
      const coreJobFamilies = data.job_families.map((family: any) => ({
        name: family.name,                    // Job Family name
        count: family.count,                  // Employee Count (user input)
        avgSalary: family.avgSalary,         // Avg Salary (user input)
        aiAdoption: family.aiAdoption        // Productivity Gain % (user input)
      }))

      // Save only core data - calculations will be done dynamically
      const coreData = {
        employee_stats: data.employee_stats,  // Aggregated stats
        job_families: coreJobFamilies,        // Core data only
        lastUpdated: new Date().toISOString()
      }

      console.log('💾 Core data to persist:', {
        jobFamiliesCount: coreJobFamilies.length,
        totalEmployees: data.employee_stats.total_employees,
        coreFields: ['name', 'count', 'avgSalary', 'aiAdoption']
      })

      // Save to localStorage (temporary solution)
      localStorage.setItem('employeeCoreData', JSON.stringify(coreData))
      console.log('💾 Saved core employee data to localStorage')

    } catch (error) {
      console.error('Failed to save core data:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-8 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading employee data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-center text-red-600">
          <p>Error loading employee data: {error}</p>
          <Button onClick={fetchEmployeeData} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  if (!employeeData) {
    return (
      <div className="p-8">
        <div className="text-center text-gray-600">
          <p>No employee data available</p>
        </div>
      </div>
    )
  }

  const employeeStats = [
    {
      title: "Total Employees",
      value: employeeData.employee_stats.total_employees.toString(),
      change: `across ${employeeData.job_families.length} job families`,
      icon: Users,
      color: "blue"
    },
    {
      title: "Average Salary",
      value: employeeData.employee_stats.average_salary,
      change: "weighted average",
      icon: DollarSign,
      color: "green"
    },
    {
      title: "Productivity Gain",
      value: employeeData.employee_stats.productivity_gain,
      change: "AI-driven efficiency",
      icon: TrendingUp,
      color: "orange"
    },
  ]

  return (
    <div className="p-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Employee Management</h1>
          <p className="text-gray-600 mt-1">Manage employee data and track productivity gains</p>
        </div>
        <Button
          onClick={handleAddGroupClick}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all"
        >
          <Plus className="h-4 w-4 mr-2" />
          {hrIntegrationEnabled ? 'Manage Groups' : 'Add Employee Group'}
        </Button>
      </div>

      {/* Employee Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {employeeStats.map((stat, index) => {
          const Icon = stat.icon
          const isProductivityGain = stat.title === "Productivity Gain"
          const hasActiveAITools = aiToolsData.some(tool => tool.status === 'active')
          const isDisabled = isProductivityGain && !hasActiveAITools

          return (
            <Card key={index} className={`bg-white/60 backdrop-blur-sm border-gray-200/50 ${isDisabled ? 'opacity-50' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-12 h-12 rounded-xl bg-gradient-to-r ${
                      isDisabled
                        ? "from-gray-400 to-gray-500"
                        : stat.color === "blue"
                          ? "from-blue-500 to-blue-600"
                          : stat.color === "green"
                            ? "from-green-500 to-green-600"
                            : stat.color === "purple"
                              ? "from-purple-500 to-purple-600"
                              : "from-orange-500 to-orange-600"
                    } flex items-center justify-center`}
                  >
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm ${isDisabled ? 'text-gray-400' : 'text-gray-600'}`}>{stat.title}</p>
                    <p className={`text-2xl font-bold ${isDisabled ? 'text-gray-400' : 'text-gray-900'}`}>{stat.value}</p>
                    <p className={`text-sm ${isDisabled ? 'text-gray-400' : 'text-gray-500'}`}>
                      {isDisabled ? 'requires active AI tools' : stat.change}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Job Families Table */}
      <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
        <CardHeader>
          <CardTitle>Job Family Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Job Family</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Employee Count</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Avg Salary</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Productivity Gain</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">ROI Impact</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Annual Savings</th>
                </tr>
              </thead>
              <tbody>
                {employeeData.job_families.map((family, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50/50">
                    <td className="py-4 px-4 font-medium text-gray-900">{family.name}</td>
                    <td className="py-4 px-4">
                      <EditableCount
                        jobFamily={family.name}
                        currentCount={family.count}
                        onCountUpdate={handleCountUpdate}
                      />
                    </td>
                    <td className="py-4 px-4">
                      <EditableSalary
                        jobFamily={family.name}
                        currentSalary={family.avgSalary}
                        onSalaryUpdate={handleSalaryUpdate}
                      />
                    </td>
                    <td className="py-4 px-4">
                      <div className={`flex items-center gap-2 ${!aiToolsData.some(tool => tool.status === 'active') ? 'opacity-50' : ''}`}>
                        <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full rounded-full ${
                              !aiToolsData.some(tool => tool.status === 'active')
                                ? 'bg-gray-400'
                                : 'bg-gradient-to-r from-blue-500 to-purple-500'
                            }`}
                            style={{ width: family.aiAdoption }}
                          ></div>
                        </div>
                        <button
                          onClick={!aiToolsData.some(tool => tool.status === 'active') ? undefined : (e) => handleAdoptionClick(e, family.name, family.aiAdoption)}
                          className={`text-sm font-medium px-2 py-1 rounded transition-colors ${
                            !aiToolsData.some(tool => tool.status === 'active')
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 cursor-pointer'
                          }`}
                          disabled={!aiToolsData.some(tool => tool.status === 'active')}
                        >
                          {family.aiAdoption}
                        </button>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={
                        !aiToolsData.some(tool => tool.status === 'active')
                          ? "bg-gray-100 text-gray-500"
                          : family.roi.startsWith('-')
                            ? "bg-red-100 text-red-800"
                            : "bg-green-100 text-green-800"
                      }>
                        {family.roi}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`text-sm font-medium ${
                        !aiToolsData.some(tool => tool.status === 'active')
                          ? 'text-gray-400'
                          : family.savings?.startsWith('+') ? 'text-green-600' :
                            family.savings?.startsWith('-') ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {family.savings || 'N/A'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Slider Adjustment Popup */}
      {sliderPopup && (
        <SliderAdjustment
          currentValue={sliderPopup.currentValue}
          onValueChange={(newValue: string) => handleSliderValueChange(sliderPopup.jobFamily, newValue)}
          onClose={handleSliderClose}
          position={sliderPopup.position}
        />
      )}

      {/* Add Employee Group Modal */}
      <AddEmployeeGroupModal
        isOpen={isAddGroupModalOpen}
        onClose={handleAddGroupClose}
        onSubmit={handleAddGroupSubmit}
        existingGroups={existingGroups}
        hrIntegrationEnabled={hrIntegrationEnabled}
        hasManualData={hasManualData}
        allowManualAdditions={allowManualAdditions}
      />
    </div>
  )
}
