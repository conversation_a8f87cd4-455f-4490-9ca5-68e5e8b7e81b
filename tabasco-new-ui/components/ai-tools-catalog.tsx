"use client"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Plus, RefreshCw, Loader2 } from "lucide-react"
import { aiToolsAPI } from "@/lib/api"

interface CatalogTool {
  key: string
  name: string
  provider: string
  description: string
  features: string[]
  monthly_price: number
  category: string
}

interface AIToolsCatalogProps {
  isOpen: boolean
  onClose: () => void
  onAddTool: (tool: CatalogTool) => void
  existingTools: any[]
}

export default function AIToolsCatalog({ isOpen, onClose, onAddTool, existingTools }: AIToolsCatalogProps) {
  const [catalogTools, setCatalogTools] = useState<CatalogTool[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch scraper data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchScraperData()
    }
  }, [isOpen])

  const fetchScraperData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await aiToolsAPI.getScraperData()
      setCatalogTools(response.tools_catalog || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load catalog')
      // Fallback to static data if API fails
      setCatalogTools([
        {
          key: "chatgpt-plus",
          name: "OpenAI ChatGPT Plus",
          provider: "OpenAI",
          description: "Enhanced version of ChatGPT for individuals and professionals.",
          features: ["Access to GPT-4", "Faster response times", "Priority access to new features", "Web browsing capabilities"],
          monthly_price: 20,
          category: "General AI Assistant",
        },
        {
          key: "github-copilot",
          name: "GitHub Copilot Business",
          provider: "Microsoft",
          description: "AI-powered code completion tool for businesses.",
          features: ["AI pair programmer", "Code suggestions in IDE", "Organization-wide policy management", "Audit logs"],
          monthly_price: 19,
          category: "Code Generation",
        },
        {
          key: "gemini-advanced",
          name: "Google Gemini Advanced",
          provider: "Google",
          description: "Google's premium AI offering with advanced capabilities.",
          features: ["Access to Google's most capable AI model", "Integration with Google Workspace", "Advanced reasoning", "Multimodal understanding"],
          monthly_price: 20,
          category: "General AI Assistant",
        },
        {
          key: "claude-pro",
          name: "Anthropic Claude Pro",
          provider: "Anthropic",
          description: "Professional version of Claude AI assistant with enhanced capabilities.",
          features: ["Larger context window (200K tokens)", "Faster processing for long documents", "Priority access", "Constitutional AI safety"],
          monthly_price: 20,
          category: "General AI Assistant",
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  const handleAddTool = (tool: CatalogTool) => {
    onAddTool(tool)
  }

  const isToolAlreadyAdded = (tool: CatalogTool) => {
    return existingTools.some(existingTool =>
      existingTool.name.toLowerCase() === tool.name.toLowerCase() ||
      (existingTool.name.toLowerCase().includes(tool.name.toLowerCase().split(' ')[0]) &&
       existingTool.provider.toLowerCase() === tool.provider.toLowerCase())
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">AI Tools Catalog</DialogTitle>
          <DialogDescription className="text-gray-600 mt-1">
            Browse and add AI tools with real-time pricing data
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading catalog...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error: {error}</p>
            <Button onClick={fetchScraperData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {catalogTools.map((tool) => (
              <Card key={tool.key} className="bg-white border-gray-200 hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-bold">{tool.name}</CardTitle>
                      <p className="text-sm text-gray-600">{tool.provider}</p>
                      <Badge variant="secondary" className="mt-1">{tool.category}</Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">${tool.monthly_price}/mo</div>
                      <div className="text-sm text-gray-500">per license</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-700">{tool.description}</p>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Features:</h4>
                    <ul className="space-y-1">
                      {tool.features.slice(0, 4).map((feature, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-center">
                          <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                      {tool.features.length > 4 && (
                        <li className="text-sm text-gray-500 italic">
                          +{tool.features.length - 4} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  {isToolAlreadyAdded(tool) ? (
                    <Button disabled className="w-full bg-gray-300 text-gray-500 cursor-not-allowed">
                      Already Added
                    </Button>
                  ) : (
                    <Button onClick={() => handleAddTool(tool)} className="w-full bg-gray-900 text-white hover:bg-gray-800">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Tool
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
