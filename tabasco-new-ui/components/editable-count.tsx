"use client"

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Check, X, Edit3, Loader2, Users } from 'lucide-react'
import { employeeManagementAPI } from '@/lib/api'

interface EditableCountProps {
  jobFamily: string
  currentCount: number
  onCountUpdate: (jobFamily: string, newCount: number, newSalary: string, newRoi: string, newSavings: string) => void
}

export default function EditableCount({
  jobFamily,
  currentCount,
  onCountUpdate
}: EditableCountProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isEditing) {
      setEditValue(currentCount.toString())
    }
  }, [isEditing, currentCount])

  const handleEdit = () => {
    setIsEditing(true)
    setError(null)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditValue('')
    setError(null)
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const newCount = parseInt(editValue)
      if (isNaN(newCount) || newCount < 0) {
        setError('Please enter a valid employee count')
        return
      }

      if (newCount > 1000) {
        setError('Employee count cannot exceed 1,000')
        return
      }

      if (newCount === currentCount) {
        setIsEditing(false)
        return
      }

      console.log(`🔄 Updating employee count for ${jobFamily}: ${currentCount} → ${newCount}`)

      const result = await employeeManagementAPI.updateJobFamilyCount(jobFamily, newCount)

      console.log('✅ Employee count update result:', result)

      // Format new salary for display
      const newDisplaySalary = result.new_avg_salary > 0
        ? `$${(result.new_avg_salary / 1000).toFixed(0)}K`
        : '$0K'

      // Update parent component with new values
      onCountUpdate(jobFamily, result.new_count, newDisplaySalary, result.new_roi, result.new_savings)

      setIsEditing(false)
      setEditValue('')

    } catch (error) {
      console.error('Failed to update employee count:', error)
      setError(error instanceof Error ? error.message : 'Failed to update employee count')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2 relative">
        <div className="relative w-24">
          <Users className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="number"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyPress}
            className={`pl-8 pr-2 py-1 text-sm h-8 text-center ${error ? 'border-red-300 focus:border-red-500' : ''}`}
            placeholder="10"
            autoFocus
            disabled={isLoading}
            min="0"
            max="1000"
          />
        </div>
        <div className="flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleSave}
            disabled={isLoading}
            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Check className="h-4 w-4" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCancel}
            disabled={isLoading}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200 whitespace-nowrap z-10">
            {error}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2 group">
      <span className="text-sm font-medium text-gray-900">
        {currentCount}
      </span>
      <Button
        size="sm"
        variant="ghost"
        onClick={handleEdit}
        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600"
      >
        <Edit3 className="h-3 w-3" />
      </Button>
    </div>
  )
}
