"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Bot, Plus, DollarSign, RefreshCw, ShoppingCart, Users, Loader2, Trash2, Edit2, Check, X } from "lucide-react"
import { aiToolsAPI, employeeManagementAPI } from "@/lib/api"
import AIToolsCatalog from "@/components/ai-tools-catalog"
import AddCustomToolModal from "@/components/add-custom-tool-modal"

interface AITool {
  id: number
  name: string
  provider: string
  category: string
  monthly_cost: number
  usage_level: string
  efficiency_rating: number
  user_count: number
  status: "active" | "inactive"
}

interface ToolStats {
  total_tools: number
  active_tools: number
  monthly_spend: number
  total_users: number
  avg_efficiency: number
  adoption_rate: number
  annual_spend: number
}

export default function AIToolsManagement() {
  const [showCatalog, setShowCatalog] = useState(false)
  const [showCustomModal, setShowCustomModal] = useState(false)
  const [aiTools, setAiTools] = useState<AITool[]>([])
  const [stats, setStats] = useState<ToolStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalEmployees, setTotalEmployees] = useState(0)
  const [editingUserId, setEditingUserId] = useState<number | null>(null)
  const [editingUserCount, setEditingUserCount] = useState<string>("")

  // Fetch data on component mount
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch tools, stats, and employee data in parallel
      const [toolsData, statsData, employeeData] = await Promise.all([
        aiToolsAPI.getAll(),
        aiToolsAPI.getStats(),
        employeeManagementAPI.getEmployeeData()
      ])

      setAiTools(toolsData)
      setStats(statsData)

      // Calculate total employees from employee data
      const totalEmp = employeeData?.employee_stats?.total_employees || 0
      setTotalEmployees(totalEmp)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load AI tools data')
    } finally {
      setLoading(false)
    }
  }

  const toolStatsDisplay = [
    {
      title: "Active Tools",
      value: stats?.active_tools?.toString() || "0",
      change: `Total: ${stats?.total_tools || 0}`,
      icon: Bot,
      color: "blue",
    },
    {
      title: "Monthly Spend",
      value: `$${(stats?.monthly_spend || 0).toLocaleString()}`,
      change: `$${(stats?.annual_spend || 0).toLocaleString()}/yr`,
      icon: DollarSign,
      color: "green"
    },
    {
      title: "Total Users",
      value: `${stats?.total_users || 0}`,
      change: `across ${stats?.active_tools || 0} tools`,
      icon: Users,
      color: "purple"
    },
    {
      title: "User Adoption",
      value: `${stats?.adoption_rate || 0}%`,
      change: "company-wide",
      icon: Users,
      color: "orange"
    },
  ]

  const toggleToolStatus = async (toolId: number) => {
    try {
      const tool = aiTools.find(t => t.id === toolId)
      if (!tool) return

      const newStatus = tool.status === "active" ? "inactive" : "active"
      await aiToolsAPI.update(toolId.toString(), { status: newStatus })

      // Update local state
      setAiTools((prev) =>
        prev.map((t) =>
          t.id === toolId ? { ...t, status: newStatus } : t,
        ),
      )

      // Refresh stats to reflect the cost changes
      const statsData = await aiToolsAPI.getStats()
      setStats(statsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update tool status')
    }
  }

  const addToolFromCatalog = async (catalogTool: any) => {
    try {
      // Check if tool already exists
      const existingTool = aiTools.find(tool =>
        tool.name.toLowerCase() === catalogTool.name.toLowerCase() ||
        (tool.name.toLowerCase().includes(catalogTool.name.toLowerCase().split(' ')[0]) &&
         tool.provider.toLowerCase() === catalogTool.provider.toLowerCase())
      )

      if (existingTool) {
        setError(`${catalogTool.name} is already added to your tools`)
        return
      }

      const newTool = {
        name: catalogTool.name,
        provider: catalogTool.provider,
        category: catalogTool.category,
        monthly_cost: catalogTool.monthly_price, // Per-license cost
        usage_level: 'medium',
        efficiency_rating: 0,
        user_count: totalEmployees, // Default to total employees
        status: 'active'
      }

      await aiToolsAPI.create(newTool)
      await fetchData() // Refresh data
      setShowCatalog(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add tool')
    }
  }

  const startEditingUserCount = (toolId: number, currentCount: number) => {
    setEditingUserId(toolId)
    setEditingUserCount(currentCount.toString())
  }

  const saveUserCount = async (toolId: number) => {
    try {
      const newCount = parseInt(editingUserCount)
      if (isNaN(newCount) || newCount < 0) {
        setError('Please enter a valid number of users')
        return
      }

      await aiToolsAPI.update(toolId.toString(), { user_count: newCount })

      // Update local state
      setAiTools(prev => prev.map(tool =>
        tool.id === toolId ? { ...tool, user_count: newCount } : tool
      ))

      setEditingUserId(null)
      setEditingUserCount("")

      // Refresh stats to reflect changes
      const statsData = await aiToolsAPI.getStats()
      setStats(statsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user count')
    }
  }

  const cancelEditingUserCount = () => {
    setEditingUserId(null)
    setEditingUserCount("")
  }

  const deleteTool = async (toolId: number) => {
    try {
      await aiToolsAPI.delete(toolId.toString())
      await fetchData() // Refresh data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete tool')
    }
  }

  const addCustomTool = async (customTool: any) => {
    try {
      const newTool = {
        name: customTool.name,
        provider: customTool.provider || "Custom",
        category: customTool.category || "Custom Tool",
        monthly_cost: customTool.price, // Store per-license cost
        usage_level: 'medium',
        efficiency_rating: 0,
        user_count: customTool.employeeCount || totalEmployees, // Use specified employee count or default
        status: 'active'
      }

      await aiToolsAPI.create(newTool)
      await fetchData() // Refresh data
      setShowCustomModal(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add custom tool')
    }
  }



  if (loading) {
    return (
      <div className="p-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading AI tools...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">Error: {error}</p>
          <Button onClick={fetchData} className="mt-2">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Tools Management</h1>
          <p className="text-gray-600 mt-1">Monitor and manage your AI tool portfolio</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="border-gray-300" onClick={fetchData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowCatalog(true)}
            className="border-blue-300 text-blue-700 hover:bg-blue-50"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Browse Catalog
          </Button>
          <Button
            onClick={() => setShowCustomModal(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Tool
          </Button>
        </div>
      </div>

      {/* Tool Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {toolStatsDisplay.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index} className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-12 h-12 rounded-xl bg-gradient-to-r ${
                      stat.color === "blue"
                        ? "from-blue-500 to-blue-600"
                        : stat.color === "green"
                          ? "from-green-500 to-green-600"
                          : stat.color === "purple"
                            ? "from-purple-500 to-purple-600"
                            : "from-orange-500 to-orange-600"
                    } flex items-center justify-center`}
                  >
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* AI Tools Table */}
      <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
        <CardHeader>
          <CardTitle>Active AI Tools</CardTitle>
        </CardHeader>
        <CardContent>
          {aiTools.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                <Bot className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No AI tools configured yet</h3>
              <p className="text-gray-600 mb-6">Get started by browsing our catalog or adding a custom tool</p>
              <div className="flex gap-3 justify-center">
                <Button onClick={() => setShowCatalog(true)} className="bg-gray-900 text-white hover:bg-gray-800">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Browse Catalog
                </Button>
                <Button onClick={() => setShowCustomModal(true)} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Tool
                </Button>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tool Name</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Provider</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Category</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Active Users</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Monthly Cost</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {aiTools.map((tool) => (
                    <tr key={tool.id} className={`border-b border-gray-100 hover:bg-gray-50/50 ${tool.status === 'inactive' ? 'opacity-60 bg-gray-50/30' : ''}`}>
                      <td className="py-4 px-4 font-medium text-gray-900">{tool.name}</td>
                      <td className="py-4 px-4 text-gray-600">{tool.provider}</td>
                      <td className="py-4 px-4">
                        <Badge variant="secondary">{tool.category}</Badge>
                      </td>
                      <td className="py-4 px-4">
                        {editingUserId === tool.id ? (
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              value={editingUserCount}
                              onChange={(e) => setEditingUserCount(e.target.value)}
                              className="w-20 h-8 text-sm"
                              min="0"
                              max={totalEmployees}
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => saveUserCount(tool.id)}
                              className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={cancelEditingUserCount}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <span className="text-gray-600">{tool.user_count}</span>
                            {tool.status === "active" && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => startEditingUserCount(tool.id, tool.user_count)}
                                className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4 text-gray-600">
                        {tool.status === "active" ? (
                          <>
                            ${(tool.monthly_cost * tool.user_count).toLocaleString()}
                            <div className="text-xs text-gray-400">
                              ${tool.monthly_cost}/user × {tool.user_count} users
                            </div>
                          </>
                        ) : (
                          <>
                            <span className="text-gray-400">$0</span>
                            <div className="text-xs text-gray-400">
                              Tool inactive
                            </div>
                          </>
                        )}
                      </td>

                      <td className="py-4 px-4">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={tool.status === "active"}
                            onCheckedChange={() => toggleToolStatus(tool.id)}
                          />
                          <Badge
                            className={
                              tool.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                            }
                          >
                            {tool.status}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => deleteTool(tool.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <AIToolsCatalog
        isOpen={showCatalog}
        onClose={() => setShowCatalog(false)}
        onAddTool={addToolFromCatalog}
        existingTools={aiTools}
      />

      <AddCustomToolModal
        isOpen={showCustomModal}
        onClose={() => setShowCustomModal(false)}
        onAddTool={addCustomTool}
      />
    </div>
  )
}
