"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

export default function ROIDashboard() {
  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <div className="flex justify-between items-center">
        <div className="border-2 border-black px-4 py-2 font-bold text-xl">ROI overview</div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input placeholder="Search employee" className="pl-10 border-2 border-black rounded-full" />
        </div>
      </div>

      {/* ROI Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="border-2 border-black px-4 py-2 text-center">
          <div className="text-sm font-medium">Employee spend</div>
          <div className="text-lg font-bold">$ 38,213,939</div>
        </div>
        <div className="border-2 border-black px-4 py-2 text-center">
          <div className="text-sm font-medium">AI spend (YTD)</div>
          <div className="text-lg font-bold">$ 38,213,939</div>
        </div>
        <div className="border-2 border-black px-4 py-2 text-center">
          <div className="text-sm font-medium">Predicted ROI</div>
          <div className="text-lg font-bold">15%</div>
        </div>
        <div className="border-2 border-black px-4 py-2 text-center">
          <div className="text-sm font-medium">Realized ROI</div>
          <div className="text-lg font-bold">12%</div>
        </div>
      </div>

      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="border-2 border-black px-4 py-2 w-fit">Usage overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Usage per job function */}
            <div className="text-center">
              <div className="border-2 border-black px-4 py-2 mb-4 font-medium">Usage per job function</div>
              <div className="w-32 h-32 mx-auto border-2 border-black rounded-full relative bg-gray-200">
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: `conic-gradient(
                    #6b7280 0deg 120deg,
                    #9ca3af 120deg 240deg,
                    #d1d5db 240deg 360deg
                  )`,
                  }}
                ></div>
              </div>
            </div>

            {/* Usage per level */}
            <div className="text-center">
              <div className="border-2 border-black px-4 py-2 mb-4 font-medium">Usage per level</div>
              <div className="w-32 h-32 mx-auto border-2 border-black rounded-full relative bg-gray-200">
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: `conic-gradient(
                    #6b7280 0deg 180deg,
                    #d1d5db 180deg 360deg
                  )`,
                  }}
                ></div>
              </div>
            </div>

            {/* Usage per model */}
            <div className="text-center">
              <div className="border-2 border-black px-4 py-2 mb-4 font-medium">Usage per model</div>
              <div className="w-32 h-32 mx-auto border-2 border-black rounded-full relative bg-gray-200">
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: `conic-gradient(
                    #6b7280 0deg 150deg,
                    #d1d5db 150deg 360deg
                  )`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
