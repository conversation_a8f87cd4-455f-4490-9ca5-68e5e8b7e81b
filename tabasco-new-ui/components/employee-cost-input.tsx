"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus } from "lucide-react"

interface Employee {
  id: string
  jobFamily: string
  level: string
  baseSalary: string
  otherComp: string
  count: string
}

export default function EmployeeCostInput() {
  const [employees, setEmployees] = useState<Employee[]>([
    {
      id: "1",
      jobFamily: "Software engineer",
      level: "L1",
      baseSalary: "165000",
      otherComp: "55000",
      count: "10",
    },
  ])

  const jobFamilies = [
    "Software engineer",
    "Research Scientist",
    "Product manager",
    "Program manager",
    "Economist",
    "Designer",
    "Finance manager",
    "Custom family",
  ]

  const levels = ["L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8", "L9"]

  const updateEmployee = (id: string, field: keyof Employee, value: string) => {
    setEmployees(employees.map((emp) => (emp.id === id ? { ...emp, [field]: value } : emp)))
  }

  const addEmployee = () => {
    const newEmployee: Employee = {
      id: Date.now().toString(),
      jobFamily: "Software engineer",
      level: "L1",
      baseSalary: "100000",
      otherComp: "20000",
      count: "1",
    }
    setEmployees([...employees, newEmployee])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-bold border-2 border-black px-4 py-2 w-fit">Employee Cost Input</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Header Row */}
          <div className="grid grid-cols-5 gap-4">
            <div className="flex items-center gap-2">
              <div className="border-2 border-black px-4 py-2 font-medium">Job family</div>
              <Button
                variant="outline"
                className="border-2 border-black rounded-full w-8 h-8 p-0"
                onClick={addEmployee}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <div className="border-2 border-black px-4 py-2 font-medium">Level</div>
              <Button variant="outline" className="border-2 border-black rounded-full w-8 h-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <div className="border-2 border-black px-4 py-2 font-medium">Base salary</div>
              <Button variant="outline" className="border-2 border-black rounded-full w-8 h-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <div className="border-2 border-black px-4 py-2 font-medium">Other comp</div>
              <Button variant="outline" className="border-2 border-black rounded-full w-8 h-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <div className="border-2 border-black px-4 py-2 font-medium">Count</div>
              <Button variant="outline" className="border-2 border-black rounded-full w-8 h-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Count Labels */}
          <div className="grid grid-cols-5 gap-4 text-sm text-gray-600">
            <div>6 entered</div>
            <div>10 entered</div>
            <div>10 entered</div>
            <div>10 entered</div>
            <div>10 entered</div>
          </div>

          {/* Employee Rows */}
          {employees.map((employee) => (
            <div key={employee.id} className="grid grid-cols-5 gap-4">
              <Select
                value={employee.jobFamily}
                onValueChange={(value) => updateEmployee(employee.id, "jobFamily", value)}
              >
                <SelectTrigger className="border-2 border-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {jobFamilies.map((family) => (
                    <SelectItem key={family} value={family}>
                      {family}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={employee.level} onValueChange={(value) => updateEmployee(employee.id, "level", value)}>
                <SelectTrigger className="border-2 border-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {levels.map((level) => (
                    <SelectItem key={level} value={level}>
                      {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={employee.baseSalary}
                onValueChange={(value) => updateEmployee(employee.id, "baseSalary", value)}
              >
                <SelectTrigger className="border-2 border-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="165000">165000</SelectItem>
                  <SelectItem value="150000">150000</SelectItem>
                  <SelectItem value="140000">140000</SelectItem>
                  <SelectItem value="130000">130000</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={employee.otherComp}
                onValueChange={(value) => updateEmployee(employee.id, "otherComp", value)}
              >
                <SelectTrigger className="border-2 border-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="55000">55000</SelectItem>
                  <SelectItem value="50000">50000</SelectItem>
                  <SelectItem value="45000">45000</SelectItem>
                  <SelectItem value="40000">40000</SelectItem>
                </SelectContent>
              </Select>

              <Select value={employee.count} onValueChange={(value) => updateEmployee(employee.id, "count", value)}>
                <SelectTrigger className="border-2 border-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="15">15</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                </SelectContent>
              </Select>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
