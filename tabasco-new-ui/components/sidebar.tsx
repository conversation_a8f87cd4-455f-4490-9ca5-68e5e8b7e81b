"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BarChart3, Users, Bot, Building, TrendingUp, Loader2 } from "lucide-react"
import { TabascoPepperIcon } from "@/components/icons/tabasco-pepper-icon"
import UserProfileMenu from "@/components/user-profile-menu"
import { quickStatsAPI } from "@/lib/api"

interface SidebarProps {
  activeView: string
  setActiveView: (view: string) => void
}

interface QuickStats {
  total_tools: number
  total_employees: number
  adoption_rate: number
  loading: boolean
  error: boolean
}

export default function Sidebar({ activeView, setActiveView }: SidebarProps) {
  const [quickStats, setQuickStats] = useState<QuickStats>({
    total_tools: 0,
    total_employees: 0,
    adoption_rate: 0,
    loading: true,
    error: false
  })
  const [refreshTimeout, setRefreshTimeout] = useState<NodeJS.Timeout | null>(null)

  const menuItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "employees", label: "Employees", icon: Users },
    { id: "ai-tools", label: "AI Tools", icon: Bot },
    { id: "hr-integration", label: "HR Integration", icon: Building, disabled: true },
  ]

  // Fetch quick stats data
  const fetchQuickStats = async () => {
    try {
      setQuickStats(prev => ({ ...prev, loading: true, error: false }))

      // Use shared API function
      const stats = await quickStatsAPI.getQuickStats()

      setQuickStats({
        total_tools: stats.total_tools,
        total_employees: stats.total_employees,
        adoption_rate: stats.adoption_rate,
        loading: false,
        error: false
      })
    } catch (error) {
      console.error('Failed to fetch quick stats:', error)
      setQuickStats(prev => ({ ...prev, loading: false, error: true }))
    }
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchQuickStats()
  }, [])

  // Refresh stats every time the active view changes (with debouncing)
  useEffect(() => {
    // Clear any existing timeout
    if (refreshTimeout) {
      clearTimeout(refreshTimeout)
    }

    // Set a new timeout to refresh stats after a short delay
    const newTimeout = setTimeout(() => {
      fetchQuickStats()
    }, 300) // 300ms delay to prevent rapid API calls

    setRefreshTimeout(newTimeout)

    // Cleanup timeout on unmount
    return () => {
      if (newTimeout) {
        clearTimeout(newTimeout)
      }
    }
  }, [activeView])

  return (
    <div className="w-64 bg-white/80 backdrop-blur-sm border-r border-gray-200/50 p-6">
      {/* Logo */}
      <div className="flex items-center gap-3 mb-8">
        <div className="w-10 h-10 bg-gradient-to-r from-red-600 to-red-700 rounded-xl flex items-center justify-center">
          <TabascoPepperIcon className="h-6 w-6 text-white" />
        </div>
        <div>
          <h1 className="text-xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent">
            Tabasco
          </h1>
          <p className="text-xs text-gray-500">AI ROI Analytics Platform</p>
        </div>
      </div>

      {/* Navigation */}
      <nav className="space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon
          const isDisabled = item.disabled
          return (
            <Button
              key={item.id}
              variant={activeView === item.id ? "default" : "ghost"}
              disabled={isDisabled}
              className={`w-full justify-start gap-3 h-12 ${
                isDisabled
                  ? "text-gray-400 cursor-not-allowed opacity-50"
                  : activeView === item.id
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-100/50"
              }`}
              onClick={() => !isDisabled && setActiveView(item.id)}
            >
              <Icon className="h-5 w-5" />
              {item.label}
              {isDisabled && (
                <span className="ml-auto text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded">
                  Disabled
                </span>
              )}
            </Button>
          )
        })}
      </nav>

      {/* Quick Stats */}
      <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100">
        <div className="flex items-center gap-2 mb-3">
          {quickStats.loading ? (
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
          ) : (
            <TrendingUp className="h-4 w-4 text-blue-600" />
          )}
          <span className="text-sm font-medium text-gray-700">Quick Stats</span>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">AI Tools</span>
            {quickStats.loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            ) : quickStats.error ? (
              <span className="text-gray-400">--</span>
            ) : (
              <span className="font-medium">{quickStats.total_tools}</span>
            )}
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Employees</span>
            {quickStats.loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            ) : quickStats.error ? (
              <span className="text-gray-400">--</span>
            ) : (
              <span className="font-medium">{quickStats.total_employees.toLocaleString()}</span>
            )}
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Adoption</span>
            {quickStats.loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            ) : quickStats.error ? (
              <span className="text-gray-400">--</span>
            ) : (
              <span className="font-medium text-green-600">{quickStats.adoption_rate.toFixed(0)}%</span>
            )}
          </div>
        </div>
      </div>

      {/* User Profile Menu */}
      <div className="mt-auto pt-4 border-t border-gray-200">
        <UserProfileMenu />
      </div>
    </div>
  )
}
