"use client"

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Check, X, Edit3, Loader2 } from 'lucide-react'
import { employeeManagementAPI } from '@/lib/api'

interface EditableSalaryProps {
  jobFamily: string
  currentSalary: string
  onSalaryUpdate: (jobFamily: string, newSalary: string, newRoi: string, newSavings: string) => void
}

export default function EditableSalary({
  jobFamily,
  currentSalary,
  onSalaryUpdate
}: EditableSalaryProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Parse current salary (e.g., "$175K" -> 175000)
  const parseDisplaySalary = (displaySalary: string): number => {
    const cleanValue = displaySalary.replace(/[$K,]/g, '')
    return parseInt(cleanValue) * 1000
  }

  // Format salary for display (e.g., 175000 -> "$175K")
  const formatDisplaySalary = (salary: number): string => {
    return `$${(salary / 1000).toFixed(0)}K`
  }

  // Format salary for editing (e.g., 175000 -> "175000")
  const formatEditSalary = (salary: number): string => {
    return salary.toString()
  }

  useEffect(() => {
    if (isEditing) {
      const numericSalary = parseDisplaySalary(currentSalary)
      setEditValue(formatEditSalary(numericSalary))
    }
  }, [isEditing, currentSalary])

  const handleEdit = () => {
    setIsEditing(true)
    setError(null)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditValue('')
    setError(null)
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const newSalary = parseInt(editValue)
      if (isNaN(newSalary) || newSalary <= 0) {
        setError('Please enter a valid salary amount')
        return
      }

      if (newSalary < 30000) {
        setError('Salary must be at least $30,000')
        return
      }

      if (newSalary > 1000000) {
        setError('Salary cannot exceed $1,000,000')
        return
      }

      console.log(`🔄 Updating salary for ${jobFamily}: ${newSalary}`)

      const result = await employeeManagementAPI.updateJobFamilySalary(jobFamily, newSalary)

      console.log('✅ Salary update result:', result)

      // Update parent component with new values
      const newDisplaySalary = formatDisplaySalary(result.new_avg_salary)
      onSalaryUpdate(jobFamily, newDisplaySalary, result.new_roi, result.new_savings)

      setIsEditing(false)
      setEditValue('')

    } catch (error) {
      console.error('Failed to update salary:', error)
      setError(error instanceof Error ? error.message : 'Failed to update salary')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2 relative">
        <div className="relative w-24">
          <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
            $
          </span>
          <Input
            type="number"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyPress}
            className={`pl-6 pr-2 py-1 text-sm h-8 text-center ${error ? 'border-red-300 focus:border-red-500' : ''}`}
            placeholder="175000"
            autoFocus
            disabled={isLoading}
          />
        </div>
        <div className="flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleSave}
            disabled={isLoading}
            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Check className="h-4 w-4" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCancel}
            disabled={isLoading}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200 whitespace-nowrap z-10">
            {error}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2 group">
      <span className="text-sm font-medium text-gray-900">
        {currentSalary}
      </span>
      <Button
        size="sm"
        variant="ghost"
        onClick={handleEdit}
        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600"
      >
        <Edit3 className="h-3 w-3" />
      </Button>
    </div>
  )
}
