# 🚀 Tabasco Deployment Guide

This guide covers deploying the Tabasco GenAI ROI Calculator to Digital Ocean App Platform.

## 📋 Prerequisites

- Digital Ocean account
- GitHub repository with the Tabasco code
- Domain name (optional)

## 🌊 Digital Ocean App Platform Deployment

### Method 1: Using App Specification File (Recommended)

1. **Fork/Clone the Repository**
   ```bash
   git clone https://github.com/mbparsa/Tabasco.git
   cd Tabasco
   git checkout feature/real-usage-analytics
   ```

2. **Deploy to Digital Ocean**
   - Go to [Digital Ocean App Platform](https://cloud.digitalocean.com/apps)
   - Click "Create App"
   - Choose "GitHub" as source
   - Select your repository and the `feature/real-usage-analytics` branch
   - Upload the `.do/app.yaml` file or configure manually:

### Manual Configuration

**Backend Service:**
- **Name:** `tabasco-backend`
- **Source Directory:** `/tabasco-backend`
- **Build Command:** (auto-detected)
- **Run Command:** `python app.py`
- **Environment:** Python
- **Port:** 5004
- **Health Check:** `/api/health`

**Environment Variables:**
```
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key-here
CORS_ORIGINS=${APP_DOMAIN}
```

**Frontend Service:**
- **Name:** `tabasco-frontend`
- **Source Directory:** `/tabasco-new-ui`
- **Build Command:** `npm run build`
- **Run Command:** `npm start`
- **Environment:** Node.js
- **Port:** 3000
- **Health Check:** `/`

**Environment Variables:**
```
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=${tabasco-backend.PUBLIC_URL}
```

### Method 2: Docker Deployment

If you prefer Docker containers:

1. **Enable Docker in App Platform**
   - In your app settings, enable "Docker" as the build method
   - Digital Ocean will automatically detect the Dockerfiles

2. **Local Testing with Docker**
   ```bash
   # Build and run locally
   docker-compose up --build
   
   # Access the application
   # Frontend: http://localhost:3000
   # Backend: http://localhost:5004
   ```

## 🔧 Configuration

### Environment Variables

**Required for Backend:**
- `FLASK_ENV=production`
- `SECRET_KEY` - Generate a secure secret key
- `CORS_ORIGINS` - Your frontend domain

**Required for Frontend:**
- `NODE_ENV=production`
- `NEXT_PUBLIC_API_BASE_URL` - Your backend URL

### Database

The app uses SQLite by default. For production, consider:
- Digital Ocean Managed PostgreSQL
- Set `DATABASE_URL` environment variable

## 🚀 Deployment Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "feat: Add Digital Ocean deployment configuration"
   git push origin feature/real-usage-analytics
   ```

2. **Create App in Digital Ocean**
   - Use the app specification file (`.do/app.yaml`)
   - Or configure manually as described above

3. **Configure Environment Variables**
   - Set production values for all required variables
   - Generate a secure `SECRET_KEY`

4. **Deploy**
   - Digital Ocean will automatically build and deploy
   - Monitor the build logs for any issues

## 🔍 Monitoring

### Health Checks
- Backend: `https://your-app.ondigitalocean.app/api/health`
- Frontend: `https://your-app.ondigitalocean.app/`

### Logs
- View logs in Digital Ocean App Platform console
- Backend logs include application and error logs
- Frontend logs include build and runtime logs

## 🛠️ Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js/Python versions in requirements
   - Verify all dependencies are listed

2. **Environment Variables**
   - Ensure all required variables are set
   - Check variable names match exactly

3. **CORS Issues**
   - Verify `CORS_ORIGINS` includes your frontend domain
   - Check `NEXT_PUBLIC_API_BASE_URL` points to backend

### Support

- Check Digital Ocean App Platform documentation
- Review build and runtime logs
- Verify health check endpoints

## 📊 Features Included

✅ Real usage analytics with actual employee data
✅ Dynamic dashboard with live calculations
✅ Production-ready configuration
✅ Health checks and monitoring
✅ Secure environment variable handling
✅ CORS configuration for cross-origin requests
✅ Database initialization and migrations
✅ Error handling and logging

## 🎯 Next Steps

After deployment:
1. Set up custom domain (optional)
2. Configure SSL certificate
3. Set up monitoring and alerts
4. Configure backup strategy for database
5. Set up CI/CD pipeline for automatic deployments
