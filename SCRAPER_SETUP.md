# Tabasco AI Tools Pricing Scraper Setup

## Overview
The Tabasco scraper automatically collects pricing data from AI tool providers every 5 hours to keep the tool catalog up-to-date.

## Features
- ✅ **Automated Collection**: Runs every 5 hours
- ✅ **12 AI Tools**: GitHub Copilot, ChatGPT, <PERSON>, <PERSON>, and more
- ✅ **Fallback Data**: Manual pricing when scraping fails
- ✅ **Data Validation**: Ensures pricing data quality
- ✅ **Backup System**: Keeps 7 days of pricing history
- ✅ **Error Handling**: Robust error recovery and logging

## Quick Setup

### 1. Install Dependencies
```bash
cd price-scraper
pip install -r scraper-requirements.txt
```

### 2. Test the Scraper
```bash
python test_scraper.py
```

### 3. Run Manual Collection
```bash
python scraper.py
```

### 4. Start Automated Collection (Every 5 Hours)
```bash
python automated_scraper.py
```

## Detailed Setup

### Prerequisites
- Python 3.8+
- Chrome browser (for Selenium fallback)
- Internet connection

### Installation Steps

#### 1. Install Python Dependencies
```bash
# Navigate to scraper directory
cd price-scraper

# Install required packages
pip install -r scraper-requirements.txt

# For Chrome/Selenium support (optional)
# Download ChromeDriver from https://chromedriver.chromium.org/
```

#### 2. Configuration
The scraper is pre-configured to collect data from these services:

**Code Generation Tools:**
- GitHub Copilot ($10-19/month)
- Cursor ($20/month)
- Codeium ($0-12/month)
- Tabnine ($12-39/month)

**General AI Assistants:**
- OpenAI ChatGPT Plus ($20-200/month)
- Anthropic Claude ($20-25/month)
- Google Gemini Advanced ($20/month)
- Perplexity Pro ($20/month)

**Specialized Tools:**
- Notion AI ($10/month)
- Jasper ($39-99/month)
- Grammarly Business ($15/month)
- Midjourney ($10-60/month)

#### 3. Test Configuration
```bash
# Test individual service
python test_scraper.py

# Expected output:
# 🚀 Testing all services...
# 🔍 Testing github_copilot...
#    Status: success
#    Plans found: 2
#      - individual: USD 10/month
#      - business: USD 19/month
```

## Running the Scraper

### Manual Collection
```bash
# Run once to collect current pricing
python scraper.py

# Output files created:
# - output/genai_pricing.yaml
# - output/genai_pricing.json
# - tabasco-backend/genai_tool_pricing.yaml (for app)
```

### Automated Collection
```bash
# Start automated scraper (runs every 5 hours)
python automated_scraper.py

# Output:
# 🌶️ Starting Tabasco Automated Scraper
# 🕐 Scheduler started - scraping every 5 hours
# Press Ctrl+C to stop
```

### Background Service (Linux/Mac)
```bash
# Create systemd service (Linux)
sudo nano /etc/systemd/system/tabasco-scraper.service

# Add:
[Unit]
Description=Tabasco AI Tools Pricing Scraper
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/price-scraper
ExecStart=/usr/bin/python3 automated_scraper.py
Restart=always

[Install]
WantedBy=multi-user.target

# Enable and start
sudo systemctl enable tabasco-scraper
sudo systemctl start tabasco-scraper
```

## Monitoring and Logs

### Log Files
```bash
# View scraper logs
tail -f scraper.log

# View scraper statistics
cat scraper_stats.json
```

### Statistics Tracking
The scraper tracks:
- Total runs and success rate
- Per-service success rates
- Last successful collection time
- Error history (last 10 errors)

### Example Stats Output
```json
{
  "total_runs": 48,
  "successful_runs": 45,
  "last_successful_run": "2024-01-15T14:30:00",
  "services_success_rate": {
    "github_copilot": {"success": 47, "total": 48},
    "openai_chatgpt": {"success": 45, "total": 48},
    "anthropic_claude": {"success": 44, "total": 48}
  }
}
```

## Data Output

### Tabasco Format (genai_tool_pricing.yaml)
```yaml
ChatGPT-Plus:
  name: "OpenAI ChatGPT Plus"
  provider: "OpenAI"
  category: "General AI Assistant"
  price_per_license_usd_monthly: 20
  features: []
  description: "AI tool from OpenAI"
  last_updated: "2024-01-15T14:30:00"
```

### Raw Scraper Format (genai_pricing.yaml)
```yaml
genai_pricing_data:
  services:
    openai_chatgpt:
      name: "OpenAI ChatGPT Plus"
      status: "success"
      pricing_plans:
        plus:
          price: 20.0
          currency: "USD"
          period: "month"
```

## Troubleshooting

### Common Issues

#### 1. Scraping Failures
```bash
# Check if websites are accessible
curl -I https://github.com/features/copilot

# Test with manual fallback
python -c "from scraper import PricingScraper; s = PricingScraper(); print(s._load_manual_pricing('github_copilot'))"
```

#### 2. Missing Dependencies
```bash
# Install missing packages
pip install schedule lxml

# For Selenium issues
pip install selenium webdriver-manager
```

#### 3. Permission Errors
```bash
# Fix file permissions
chmod +x automated_scraper.py
chmod 755 output/
```

#### 4. Network Issues
```bash
# Test network connectivity
ping github.com

# Check proxy settings if behind corporate firewall
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

### Manual Pricing Updates

If scraping fails for a service, update manual pricing:

```bash
# Edit manual pricing file
nano price-scraper/manual_pricing.yaml

# Add or update service pricing
openai_chatgpt:
  name: "OpenAI ChatGPT Plus"
  pricing_plans:
    plus:
      price: 20.0  # Update this price
      currency: "USD"
      billing_period: "month"
```

## Integration with Tabasco

### Automatic Updates
The scraper automatically updates:
1. `tabasco-backend/genai_tool_pricing.yaml` - Used by the app
2. Creates backups in `backups/` directory
3. Logs all activities to `scraper.log`

### Manual Integration
```bash
# Copy latest pricing to Tabasco backend
cp output/genai_pricing.yaml tabasco-backend/genai_tool_pricing.yaml

# Restart Tabasco backend to load new data
cd tabasco-backend
python app.py
```

## Performance and Reliability

### Scraping Schedule
- **Frequency**: Every 5 hours
- **Backup**: Daily backups kept for 7 days
- **Retry Logic**: 3 attempts per service
- **Timeout**: 30 seconds per request
- **Rate Limiting**: 3-second delays between requests

### Success Rates
Typical success rates by service type:
- **GitHub/Microsoft**: 95%+ (stable APIs)
- **OpenAI/Anthropic**: 90%+ (occasional rate limits)
- **Smaller providers**: 80%+ (more variable)

### Fallback Strategy
1. **Primary**: Web scraping from pricing pages
2. **Secondary**: Manual pricing data (updated monthly)
3. **Tertiary**: Previous successful scrape data

The scraper ensures **continuous data availability** even when individual services are temporarily unavailable! 🚀📊🤖✨
